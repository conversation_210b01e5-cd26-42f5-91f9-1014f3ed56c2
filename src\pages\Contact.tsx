import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { cn } from "@/utils";
import { imageAssets } from "@/utils/imageAssets";
import FallbackImage from "@/components/ui/FallbackImage";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import PageHeader from "@/components/ui/PageHeader";

interface ContactForm {
  name: string;
  phone: string;
  email: string;
  subject: string;
  message: string;
}

const Contact: React.FC = () => {
  const [formData, setFormData] = useState<ContactForm>({
    name: "",
    phone: "",
    email: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 联系信息
  const contactInfo = [
    {
      icon: "📞",
      title: "客服热线",
      content: "0731-24688888",
      description: "24小时为您服务",
      action: "tel:0731-24688888",
    },
    {
      icon: "📧",
      title: "邮箱地址",
      content: "<EMAIL>",
      description: "工作日内24小时回复",
      action: "mailto:<EMAIL>",
    },
    {
      icon: "📍",
      title: "景区地址",
      content: "湖南省株洲市攸县酒埠江镇",
      description: "距离攸县县城约30公里",
      action: null,
    },
    {
      icon: "🕒",
      title: "开放时间",
      content: "08:00 - 18:00",
      description: "全年无休，节假日正常开放",
      action: null,
    },
  ];

  // 交通指南
  const transportGuides = [
    {
      method: "自驾车",
      routes: [
        "长沙方向：长株高速 → 醴茶高速 → 攸县出口 → 酒埠江镇",
        "株洲方向：S211省道 → 攸县 → 酒埠江镇",
        "衡阳方向：S315省道 → 攸县 → 酒埠江镇",
      ],
      time: "约1-2小时",
      parking: "景区内设有大型停车场，免费停车",
    },
    {
      method: "公共交通",
      routes: [
        "高铁：长沙南站/株洲西站 → 攸县站 → 景区专线巴士",
        "汽车：各地汽车站 → 攸县汽车站 → 景区专线巴士",
        "飞机：长沙黄花机场 → 机场大巴 → 攸县 → 景区专线巴士",
      ],
      time: "约2-3小时",
      parking: "景区提供接驳服务",
    },
  ];

  // 常见问题
  const faqs = [
    {
      question: "景区门票价格是多少？",
      answer:
        "成人票68元/人，学生票、老人票享受优惠价格。具体优惠政策请咨询客服。",
    },
    {
      question: "景区内有住宿吗？",
      answer: "景区内有多种住宿选择，包括湖景客房、特色民宿等，可提前预订。",
    },
    {
      question: "可以带宠物入园吗？",
      answer: "为了保护生态环境和其他游客的安全，景区内不允许携带宠物。",
    },
    {
      question: "景区内有餐厅吗？",
      answer: "景区内有多家餐厅，提供湘菜、湖鲜等特色美食，也有素食餐厅。",
    },
  ];

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // 模拟提交过程
    await new Promise((resolve) => setTimeout(resolve, 2000));

    alert("感谢您的留言！我们会尽快回复您。");
    setFormData({
      name: "",
      phone: "",
      email: "",
      subject: "",
      message: "",
    });
    setIsSubmitting(false);
  };

  return (
    <Layout>
      {/* 页面头部 */}
      <PageHeader
        title="联系我们"
        subtitle="我们随时为您提供专业的咨询和服务"
        backgroundImage={imageAssets.backgrounds.sectionBg1}
      />

      {/* 联系信息 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {contactInfo.map((info, index) => (
              <Card
                key={index}
                className="text-center p-6 hover:shadow-medium transition-all duration-300"
              >
                <div className="text-4xl mb-4">{info.icon}</div>
                <h3 className="text-lg font-semibold text-ink-black-700 mb-2">
                  {info.title}
                </h3>
                {info.action ? (
                  <a
                    href={info.action}
                    className="text-lake-green-600 font-medium hover:text-lake-green-700 transition-colors"
                  >
                    {info.content}
                  </a>
                ) : (
                  <p className="text-ink-black-600 font-medium">
                    {info.content}
                  </p>
                )}
                <p className="text-gray-500 text-sm mt-2">{info.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* 主要内容区域 */}
      <section className="py-16 bg-gray-50">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* 联系表单 */}
            <div>
              <h2 className="text-2xl font-bold text-ink-black-700 mb-6">
                在线咨询
              </h2>
              <Card className="p-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        姓名 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lake-green-500 focus:border-transparent"
                        placeholder="请输入您的姓名"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        手机号 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lake-green-500 focus:border-transparent"
                        placeholder="请输入您的手机号"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱地址
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lake-green-500 focus:border-transparent"
                      placeholder="请输入您的邮箱地址"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      咨询主题 <span className="text-red-500">*</span>
                    </label>
                    <select
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lake-green-500 focus:border-transparent"
                    >
                      <option value="">请选择咨询主题</option>
                      <option value="门票预订">门票预订</option>
                      <option value="住宿预订">住宿预订</option>
                      <option value="团体接待">团体接待</option>
                      <option value="交通咨询">交通咨询</option>
                      <option value="投诉建议">投诉建议</option>
                      <option value="其他咨询">其他咨询</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      详细内容 <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lake-green-500 focus:border-transparent resize-none"
                      placeholder="请详细描述您的问题或需求..."
                    />
                  </div>

                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "提交中..." : "提交咨询"}
                  </Button>
                </form>
              </Card>
            </div>

            {/* 交通指南 */}
            <div>
              <h2 className="text-2xl font-bold text-ink-black-700 mb-6">
                交通指南
              </h2>
              <div className="space-y-6">
                {transportGuides.map((guide, index) => (
                  <Card key={index} className="p-6">
                    <h3 className="text-lg font-semibold text-ink-black-700 mb-4 flex items-center gap-2">
                      <span className="text-2xl">
                        {index === 0 ? "🚗" : "🚌"}
                      </span>
                      {guide.method}
                    </h3>
                    <div className="space-y-3">
                      {guide.routes.map((route, routeIndex) => (
                        <div
                          key={routeIndex}
                          className="flex items-start gap-2"
                        >
                          <div className="w-1.5 h-1.5 bg-lake-green-400 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-gray-600 text-sm">{route}</span>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">
                          预计时间：{guide.time}
                        </span>
                        <span className="text-gray-500">{guide.parking}</span>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16">
        <div className="container-custom">
          <h2 className="text-2xl font-bold text-ink-black-700 mb-8 text-center">
            常见问题
          </h2>
          <div className="max-w-3xl mx-auto">
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <Card key={index} className="p-6">
                  <h3 className="text-lg font-semibold text-ink-black-700 mb-3 flex items-start gap-2">
                    <span className="text-lake-green-600 flex-shrink-0">
                      Q:
                    </span>
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 leading-relaxed pl-6">
                    <span className="text-elegant-gold-600 font-medium">
                      A:
                    </span>{" "}
                    {faq.answer}
                  </p>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 地图区域 */}
      <section className="py-16 bg-gray-50">
        <div className="container-custom">
          <h2 className="text-2xl font-bold text-ink-black-700 mb-8 text-center">
            景区位置
          </h2>
          <Card className="p-6">
            <div className="bg-gray-200 h-96 rounded-lg flex items-center justify-center">
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-4">🗺️</div>
                <p className="text-lg font-medium">地图加载中...</p>
                <p className="text-sm mt-2">湖南省株洲市攸县酒埠江镇</p>
              </div>
            </div>
          </Card>
        </div>
      </section>
    </Layout>
  );
};

export default Contact;
