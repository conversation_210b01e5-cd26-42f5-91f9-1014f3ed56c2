import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import { cn } from "@/utils";
import { imageAssets } from "@/utils/imageAssets";
import { AttractionCard } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import PageHeader from "@/components/ui/PageHeader";

interface Attraction {
  id: number;
  name: string;
  nameEn: string;
  description: string;
  image: string;
  category: string;
  rating: number;
  price: number;
  tags: string[];
  features: string[];
}

const Attractions: React.FC = () => {
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  // 景点数据
  const attractions: Attraction[] = [
    {
      id: 1,
      name: "酒仙湖水域",
      nameEn: "Jiuxian Lake",
      description:
        "总蓄水量3亿立方米，蓄水面积11.2平方公里的国家级水利风景区，湖光山色美不胜收。",
      image: imageAssets.attractions.jiuxianLake,
      category: "natural",
      rating: 4.8,
      price: 68,
      tags: ["国家4A级景区", "水利风景区", "生态旅游"],
      features: ["游船观光", "湖心岛游览", "水上运动", "摄影基地"],
    },
    {
      id: 2,
      name: "宝宁寺",
      nameEn: "Baoning Temple",
      description:
        '佛教禅宗曹洞宗祖庭，享有"北有少林，南有宝宁"美誉的千年古刹。',
      image: imageAssets.attractions.baoningTemple,
      category: "cultural",
      rating: 4.9,
      price: 30,
      tags: ["佛教圣地", "禅宗祖庭", "文化遗产"],
      features: ["禅修体验", "佛教文化", "古建筑群", "祈福许愿"],
    },
    {
      id: 3,
      name: "攸女仙境",
      nameEn: "Younv Fairyland",
      description:
        '以"攸女文化"为主题的神秘岛屿，传说中攸女的居住之地，充满浪漫色彩。',
      image: imageAssets.attractions.younvFairyland,
      category: "cultural",
      rating: 4.7,
      price: 45,
      tags: ["攸女文化", "神话传说", "浪漫圣地"],
      features: ["文化体验", "传说故事", "拍照打卡", "民俗表演"],
    },
    {
      id: 4,
      name: "白龙洞",
      nameEn: "White Dragon Cave",
      description:
        "神奇的溶洞景观，洞内钟乳石千姿百态，地下河流潺潺，是探险的绝佳去处。",
      image: imageAssets.attractions.whiteDragonCave,
      category: "natural",
      rating: 4.6,
      price: 55,
      tags: ["溶洞奇观", "地质公园", "探险体验"],
      features: ["溶洞探险", "地下河流", "钟乳石群", "地质科普"],
    },
    {
      id: 5,
      name: "地质博物馆",
      nameEn: "Geological Museum",
      description:
        "展示酒埠江地质公园特色的专业博物馆，了解地球演化历史的科普基地。",
      image: imageAssets.attractions.geologicalMuseum,
      category: "cultural",
      rating: 4.5,
      price: 25,
      tags: ["地质科普", "教育基地", "科学探索"],
      features: ["地质展览", "科普教育", "互动体验", "研学旅行"],
    },
    {
      id: 6,
      name: "温泉康养中心",
      nameEn: "Hot Spring Wellness Center",
      description: "天然温泉资源，结合现代康养理念，提供身心放松的完美体验。",
      image: imageAssets.attractions.hotSpring,
      category: "wellness",
      rating: 4.8,
      price: 128,
      tags: ["温泉养生", "康养度假", "健康体验"],
      features: ["温泉洗浴", "养生保健", "瑜伽冥想", "SPA护理"],
    },
    {
      id: 7,
      name: "官田古镇",
      nameEn: "Guantian Ancient Town",
      description:
        "保存完好的湘东古镇，青石板路、古建筑群，感受浓郁的历史文化氛围。",
      image: imageAssets.attractions.guantianTown,
      category: "cultural",
      rating: 4.4,
      price: 35,
      tags: ["古镇风情", "历史文化", "民俗体验"],
      features: ["古建筑群", "民俗文化", "特色小吃", "手工艺品"],
    },
    {
      id: 8,
      name: "筏钓基地",
      nameEn: "Raft Fishing Base",
      description:
        "专业的筏钓体验基地，在湖光山色中享受垂钓的乐趣，体验渔家生活。",
      image: imageAssets.attractions.raftFishing,
      category: "entertainment",
      rating: 4.3,
      price: 88,
      tags: ["休闲垂钓", "渔家体验", "亲子活动"],
      features: ["筏钓体验", "渔具租赁", "鱼获烹饪", "亲子互动"],
    },
  ];

  // 分类选项
  const categories = [
    { key: "all", name: "全部景点", count: attractions.length },
    {
      key: "natural",
      name: "自然景观",
      count: attractions.filter((a) => a.category === "natural").length,
    },
    {
      key: "cultural",
      name: "文化景观",
      count: attractions.filter((a) => a.category === "cultural").length,
    },
    {
      key: "wellness",
      name: "康养度假",
      count: attractions.filter((a) => a.category === "wellness").length,
    },
    {
      key: "entertainment",
      name: "娱乐体验",
      count: attractions.filter((a) => a.category === "entertainment").length,
    },
  ];

  // 过滤景点
  const filteredAttractions =
    selectedCategory === "all"
      ? attractions
      : attractions.filter(
          (attraction) => attraction.category === selectedCategory
        );

  const handleAttractionClick = (id: number) => {
    navigate(`/attractions/${id}`);
  };

  return (
    <Layout>
      {/* 页面头部 */}
      <PageHeader
        title="景点介绍"
        subtitle="探索酒仙湖八大特色景点，感受湘东明珠的独特魅力"
        backgroundImage={imageAssets.attractions.jiuxianLake}
      />

      {/* 分类筛选 */}
      <section className="py-8 bg-gray-50">
        <div className="container-custom">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.key}
                onClick={() => setSelectedCategory(category.key)}
                className={cn(
                  "px-6 py-3 rounded-full font-medium transition-all duration-300",
                  selectedCategory === category.key
                    ? "bg-lake-green-400 text-white shadow-md"
                    : "bg-white text-gray-600 hover:bg-lake-green-50 hover:text-lake-green-600"
                )}
              >
                {category.name}
                <span className="ml-2 text-sm opacity-75">
                  ({category.count})
                </span>
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* 景点网格 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredAttractions.map((attraction, index) => (
              <div
                key={attraction.id}
                className="animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <AttractionCard
                  image={attraction.image}
                  title={attraction.name}
                  description={attraction.description}
                  rating={attraction.rating}
                  price={attraction.price}
                  onClick={() => handleAttractionClick(attraction.id)}
                  className="h-full hover:shadow-strong transition-all duration-300"
                />
              </div>
            ))}
          </div>

          {filteredAttractions.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg
                  className="w-16 h-16 mx-auto"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-600 mb-2">
                暂无相关景点
              </h3>
              <p className="text-gray-500">请尝试其他分类或查看全部景点</p>
            </div>
          )}
        </div>
      </section>

      {/* 底部行动区域 */}
      <section className="py-16 bg-gradient-to-r from-lake-green-50 to-elegant-gold-50">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold text-ink-black-700 mb-4">
            开启您的酒仙湖之旅
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            八大特色景点，每一处都有独特的魅力等待您的发现
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="primary"
              size="lg"
              onClick={() => navigate("/contact")}
            >
              联系咨询
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => navigate("/services")}
            >
              查看服务
            </Button>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Attractions;
