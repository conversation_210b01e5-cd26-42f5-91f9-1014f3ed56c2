import React from 'react';
import Layout from '@/components/layout/Layout';
import { cn } from '@/utils';
import { imageAssets } from '@/utils/imageAssets';
import FallbackImage from '@/components/ui/FallbackImage';
import { Card } from '@/components/ui/Card';

const About: React.FC = () => {
  // 发展历程数据
  const milestones = [
    {
      year: '1958年',
      title: '水库建设',
      description: '酒埠江水库开始建设，为酒仙湖的形成奠定基础'
    },
    {
      year: '1985年',
      title: '景区开发',
      description: '开始进行旅游资源开发，建设基础设施'
    },
    {
      year: '2003年',
      title: '4A级景区',
      description: '成功创建国家4A级旅游景区'
    },
    {
      year: '2010年',
      title: '地质公园',
      description: '获批国家地质公园，地质旅游特色凸显'
    },
    {
      year: '2018年',
      title: '数字化升级',
      description: '启动智慧景区建设，提升游客体验'
    },
    {
      year: '2024年',
      title: '生态示范',
      description: '荣获湖南省生态旅游示范区称号'
    }
  ];

  // 核心价值观
  const values = [
    {
      icon: '🌿',
      title: '生态优先',
      description: '坚持绿色发展理念，保护自然生态环境，实现可持续发展'
    },
    {
      icon: '🏛️',
      title: '文化传承',
      description: '弘扬湖湘文化和佛教文化，传承历史文脉，彰显文化底蕴'
    },
    {
      icon: '👥',
      title: '游客至上',
      description: '以游客需求为中心，提供优质服务，创造美好体验'
    },
    {
      icon: '🚀',
      title: '创新发展',
      description: '拥抱新技术，创新服务模式，打造智慧旅游新标杆'
    }
  ];

  // 荣誉资质
  const honors = [
    '国家4A级旅游景区',
    '国家地质公园',
    '国家水利风景区',
    '湖南省生态旅游示范区',
    '湖南省文明旅游景区',
    '株洲市优秀旅游景区'
  ];

  // 统计数据
  const statistics = [
    { number: '11.2', unit: '平方公里', label: '湖面面积' },
    { number: '3', unit: '亿立方米', label: '蓄水量' },
    { number: '8', unit: '个', label: '特色景点' },
    { number: '100+', unit: '万人次', label: '年接待游客' }
  ];

  return (
    <Layout>
      {/* 页面头部 */}
      <section className="relative py-20 overflow-hidden">
        <FallbackImage
          src={imageAssets.hero.slide1}
          alt="酒仙湖风光"
          className="absolute inset-0 w-full h-full object-cover"
          fallbackType="gradient"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/30" />
        <div className="relative container-custom text-center text-white">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 brush-text">关于酒仙湖</h1>
          <p className="text-xl text-gray-100 max-w-3xl mx-auto leading-relaxed">
            湘东明珠·现代桃花源，集自然风光、佛教文化、康养度假于一体的综合性旅游目的地
          </p>
        </div>
      </section>

      {/* 景区概况 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-ink-black-700 mb-6">景区概况</h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  酒仙湖生态旅游度假区位于湖南省株洲市攸县，是国家4A级旅游景区、国家地质公园、国家水利风景区。
                  景区总面积约50平方公里，其中湖面面积11.2平方公里，蓄水量3亿立方米。
                </p>
                <p>
                  景区以酒仙湖为核心，融合了自然山水、佛教文化、地质奇观、康养度假等多元素，
                  形成了独特的"湖光山色、禅韵悠长"的旅游品牌。这里有碧波荡漾的湖水、
                  千年古刹宝宁寺、神秘的攸女仙境、奇特的白龙洞等八大特色景点。
                </p>
                <p>
                  作为湘东地区重要的旅游目的地，酒仙湖致力于打造集观光游览、文化体验、
                  康养度假、科普教育于一体的综合性生态旅游度假区，为游客提供身心愉悦的旅游体验。
                </p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {statistics.map((stat, index) => (
                <Card key={index} className="text-center p-6">
                  <div className="text-3xl font-bold text-lake-green-600 mb-2">
                    {stat.number}
                    <span className="text-lg text-gray-500 ml-1">{stat.unit}</span>
                  </div>
                  <div className="text-gray-600">{stat.label}</div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 发展历程 */}
      <section className="py-16 bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ink-black-700 mb-4">发展历程</h2>
            <p className="text-lg text-gray-600">见证酒仙湖从水库到国家级景区的华丽蜕变</p>
          </div>
          
          <div className="relative">
            {/* 时间线 */}
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-lake-green-200 hidden md:block" />
            
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div key={index} className={cn(
                  'relative flex items-center',
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                )}>
                  {/* 时间点 */}
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-lake-green-400 rounded-full border-4 border-white shadow-md hidden md:block z-10" />
                  
                  {/* 内容卡片 */}
                  <div className={cn(
                    'w-full md:w-5/12',
                    index % 2 === 0 ? 'md:pr-8' : 'md:pl-8'
                  )}>
                    <Card className="p-6 hover:shadow-medium transition-all duration-300">
                      <div className="flex items-center gap-4 mb-3">
                        <span className="bg-elegant-gold-400 text-ink-black-700 px-3 py-1 rounded-full text-sm font-medium">
                          {milestone.year}
                        </span>
                      </div>
                      <h3 className="text-xl font-semibold text-ink-black-700 mb-2">
                        {milestone.title}
                      </h3>
                      <p className="text-gray-600">{milestone.description}</p>
                    </Card>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 核心价值观 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ink-black-700 mb-4">核心价值观</h2>
            <p className="text-lg text-gray-600">指引我们前行的价值理念</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-medium transition-all duration-300">
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-semibold text-ink-black-700 mb-3">{value.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{value.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* 荣誉资质 */}
      <section className="py-16 bg-gradient-to-r from-lake-green-50 to-elegant-gold-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ink-black-700 mb-4">荣誉资质</h2>
            <p className="text-lg text-gray-600">获得的认可与荣誉</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {honors.map((honor, index) => (
              <div
                key={index}
                className="bg-white rounded-lg p-4 text-center shadow-soft hover:shadow-medium transition-all duration-300"
              >
                <div className="w-8 h-8 bg-elegant-gold-400 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-white text-sm">🏆</span>
                </div>
                <span className="text-ink-black-700 font-medium">{honor}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 企业文化 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <FallbackImage
                src={imageAssets.attractions.baoningTemple}
                alt="宝宁寺"
                className="w-full h-80 object-cover rounded-xl"
                fallbackType="pattern"
              />
            </div>
            <div>
              <h2 className="text-3xl font-bold text-ink-black-700 mb-6">企业文化</h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  <strong className="text-ink-black-700">使命：</strong>
                  传承湖湘文化，保护生态环境，为游客创造美好的旅游体验，
                  推动地方经济发展和文化传承。
                </p>
                <p>
                  <strong className="text-ink-black-700">愿景：</strong>
                  成为中南地区知名的生态文化旅游目的地，
                  打造具有国际影响力的湖湘文化旅游品牌。
                </p>
                <p>
                  <strong className="text-ink-black-700">理念：</strong>
                  秉承"生态优先、文化传承、游客至上、创新发展"的经营理念，
                  致力于可持续发展，实现经济效益、社会效益和生态效益的统一。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default About;
