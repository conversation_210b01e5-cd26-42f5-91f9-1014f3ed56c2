import React from "react";
import Layout from "@/components/layout/Layout";
import { cn } from "@/utils";
import { imageAssets } from "@/utils/imageAssets";
import FallbackImage from "@/components/ui/FallbackImage";
import Card from "@/components/ui/Card";
import PageHeader from "@/components/ui/PageHeader";

const About: React.FC = () => {
  // 公司发展历程数据
  const milestones = [
    {
      year: "1958年",
      title: "水库建设奠基",
      description:
        "酒埠江水库开始建设，为酒仙湖的形成和后续文化产业发展奠定坚实基础",
    },
    {
      year: "1985年",
      title: "旅游开发起步",
      description: "开始进行旅游资源开发，建设基础设施，确立生态旅游发展方向",
    },
    {
      year: "2003年",
      title: "4A级景区创建",
      description: "成功创建国家4A级旅游景区，标志着景区品质的重要提升",
    },
    {
      year: "2010年",
      title: "地质公园获批",
      description: "获批国家地质公园，地质旅游特色凸显，科普教育功能增强",
    },
    {
      year: "2018年",
      title: "数字化转型启动",
      description: "启动智慧景区建设，推进文旅产业数字化转型，提升游客体验",
    },
    {
      year: "2024年",
      title: "文化产业集团成立",
      description:
        "湖南酒仙湖文化产业发展有限公司正式成立，开启多元化文化产业发展新征程",
    },
  ];

  // 产业发展布局
  const businessLayout = [
    {
      icon: "🏞️",
      title: "生态旅游",
      description:
        "以酒仙湖4A级景区为核心，打造集观光、休闲、度假于一体的生态旅游目的地",
      highlights: ["国家4A级景区", "国家地质公园", "国家水利风景区"],
    },
    {
      icon: "🏛️",
      title: "文化产业",
      description:
        "深度挖掘湖湘文化、佛教文化、攸女文化等地域文化资源，发展文化创意产业",
      highlights: ["宝宁寺文化", "攸女仙境", "地质科普"],
    },
    {
      icon: "💻",
      title: "数字文旅",
      description:
        "运用现代科技手段，推进文旅产业数字化转型，打造智慧旅游新模式",
      highlights: ["智慧景区", "VR体验", "数字营销"],
    },
    {
      icon: "🌱",
      title: "生态保护",
      description: "坚持生态优先，绿色发展，建设人与自然和谐共生的美丽景区",
      highlights: ["生态修复", "环境监测", "绿色发展"],
    },
  ];

  // 核心价值观
  const values = [
    {
      icon: "🌿",
      title: "生态优先",
      description: "坚持绿色发展理念，保护自然生态环境，实现人与自然和谐共生",
    },
    {
      icon: "🏛️",
      title: "文化传承",
      description: "弘扬湖湘文化和佛教文化，传承历史文脉，彰显深厚文化底蕴",
    },
    {
      icon: "👥",
      title: "游客至上",
      description: "以游客需求为中心，提供优质服务，创造美好旅游体验",
    },
    {
      icon: "🚀",
      title: "创新发展",
      description: "拥抱新技术，创新服务模式，推动文旅产业转型升级",
    },
  ];

  // 荣誉资质
  const honors = [
    "国家4A级旅游景区",
    "国家地质公园",
    "国家水利风景区",
    "湖南省生态旅游示范区",
    "湖南省文明旅游景区",
    "株洲市优秀旅游景区",
  ];

  // 统计数据
  const statistics = [
    { number: "11.2", unit: "平方公里", label: "湖面面积" },
    { number: "3", unit: "亿立方米", label: "蓄水量" },
    { number: "8", unit: "个", label: "特色景点" },
    { number: "100+", unit: "万人次", label: "年接待游客" },
  ];

  return (
    <Layout>
      {/* 页面头部 */}
      <PageHeader
        title="湖南酒仙湖文化产业发展有限公司"
        subtitle="以酒仙湖国家4A级景区为核心，致力于文化旅游产业发展，打造集生态旅游、文化创意、数字科技于一体的现代文化产业集团"
        backgroundImage={imageAssets.hero.slide1}
      />

      {/* 景区概况 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-ink-black-700 mb-6">
                公司概况
              </h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  湖南酒仙湖文化产业发展有限公司成立于2024年，是一家以文化旅游为主导的现代文化产业集团。
                  公司以酒仙湖国家4A级景区为核心资产，景区总面积约50平方公里，湖面面积11.2平方公里，蓄水量3亿立方米。
                </p>
                <p>
                  公司秉承"生态优先、文化传承、创新发展"的理念，致力于打造集生态旅游、文化创意、
                  数字科技、生态保护于一体的现代文化产业生态圈。通过深度挖掘湖湘文化、佛教文化、
                  攸女文化等地域文化资源，推动文旅产业高质量发展。
                </p>
                <p>
                  作为湖南省文化旅游产业的重要参与者，公司致力于成为中南地区领先的文化旅游产业集团，
                  为推动区域经济发展、文化传承和生态保护作出积极贡献。
                </p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {statistics.map((stat, index) => (
                <Card key={index} className="text-center p-6">
                  <div className="text-3xl font-bold text-lake-green-600 mb-2">
                    {stat.number}
                    <span className="text-lg text-gray-500 ml-1">
                      {stat.unit}
                    </span>
                  </div>
                  <div className="text-gray-600">{stat.label}</div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 产业发展布局 */}
      <section className="py-16 bg-rice-white-100">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ink-black-700 mb-4">
              产业发展布局
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              构建以生态旅游为核心，文化产业、数字科技、生态保护协同发展的现代产业体系
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {businessLayout.map((business, index) => (
              <Card
                key={index}
                className="text-center p-6 hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{business.icon}</div>
                <h3 className="text-xl font-semibold text-ink-black-700 mb-3">
                  {business.title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {business.description}
                </p>
                <div className="space-y-2">
                  {business.highlights.map((highlight, idx) => (
                    <span
                      key={idx}
                      className="inline-block bg-lake-green-100 text-lake-green-700 text-xs px-2 py-1 rounded-full mr-1"
                    >
                      {highlight}
                    </span>
                  ))}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* 发展历程 */}
      <section className="py-16 bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ink-black-700 mb-4">
              发展历程
            </h2>
            <p className="text-lg text-gray-600">
              见证湖南酒仙湖文化产业发展有限公司从景区运营到文化产业集团的华丽蜕变
            </p>
          </div>

          <div className="relative">
            {/* 时间线 */}
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-lake-green-200 hidden md:block" />

            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div
                  key={index}
                  className={cn(
                    "relative flex items-center",
                    index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
                  )}
                >
                  {/* 时间点 */}
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-lake-green-400 rounded-full border-4 border-white shadow-md hidden md:block z-10" />

                  {/* 内容卡片 */}
                  <div
                    className={cn(
                      "w-full md:w-5/12",
                      index % 2 === 0 ? "md:pr-8" : "md:pl-8"
                    )}
                  >
                    <Card className="p-6 hover:shadow-medium transition-all duration-300">
                      <div className="flex items-center gap-4 mb-3">
                        <span className="bg-elegant-gold-400 text-ink-black-700 px-3 py-1 rounded-full text-sm font-medium">
                          {milestone.year}
                        </span>
                      </div>
                      <h3 className="text-xl font-semibold text-ink-black-700 mb-2">
                        {milestone.title}
                      </h3>
                      <p className="text-gray-600">{milestone.description}</p>
                    </Card>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 核心价值观 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ink-black-700 mb-4">
              核心价值观
            </h2>
            <p className="text-lg text-gray-600">指引我们前行的价值理念</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card
                key={index}
                className="text-center p-6 hover:shadow-medium transition-all duration-300"
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-semibold text-ink-black-700 mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {value.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* 荣誉资质 */}
      <section className="py-16 bg-gradient-to-r from-lake-green-50 to-elegant-gold-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ink-black-700 mb-4">
              荣誉资质
            </h2>
            <p className="text-lg text-gray-600">获得的认可与荣誉</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {honors.map((honor, index) => (
              <div
                key={index}
                className="bg-white rounded-lg p-4 text-center shadow-soft hover:shadow-medium transition-all duration-300"
              >
                <div className="w-8 h-8 bg-elegant-gold-400 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-white text-sm">🏆</span>
                </div>
                <span className="text-ink-black-700 font-medium">{honor}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 企业文化 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <FallbackImage
                src={imageAssets.attractions.baoningTemple}
                alt="宝宁寺"
                className="w-full h-80 object-cover rounded-xl"
                fallbackType="pattern"
              />
            </div>
            <div>
              <h2 className="text-3xl font-bold text-ink-black-700 mb-6">
                企业文化
              </h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  <strong className="text-ink-black-700">使命：</strong>
                  传承湖湘文化，保护生态环境，为游客创造美好的旅游体验，
                  推动地方经济发展和文化传承。
                </p>
                <p>
                  <strong className="text-ink-black-700">愿景：</strong>
                  成为中南地区知名的生态文化旅游目的地，
                  打造具有国际影响力的湖湘文化旅游品牌。
                </p>
                <p>
                  <strong className="text-ink-black-700">理念：</strong>
                  秉承"生态优先、文化传承、游客至上、创新发展"的经营理念，
                  致力于可持续发展，实现经济效益、社会效益和生态效益的统一。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default About;
