import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import { cn, formatDate } from "@/utils";
import { imageAssets } from "@/utils/imageAssets";
import { NewsCard } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import PageHeader from "@/components/ui/PageHeader";

interface NewsItem {
  id: number;
  title: string;
  summary: string;
  image: string;
  date: string;
  category: string;
  views: number;
  featured: boolean;
}

const News: React.FC = () => {
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 9;

  // 新闻数据
  const newsData: NewsItem[] = [
    {
      id: 1,
      title: '酒仙湖景区荣获"湖南省生态旅游示范区"称号',
      summary:
        "近日，湖南省文化和旅游厅公布了2024年度生态旅游示范区名单，酒仙湖景区凭借其优美的自然环境和完善的生态保护措施成功入选。",
      image: imageAssets.news.news1,
      date: "2024-03-15",
      category: "景区荣誉",
      views: 1250,
      featured: true,
    },
    {
      id: 2,
      title: "酒仙湖数字化升级项目正式启动",
      summary:
        "为提升游客体验，酒仙湖景区启动全面数字化升级项目，将引入VR全景游览、AR互动体验等先进技术。",
      image: imageAssets.news.news2,
      date: "2024-03-12",
      category: "项目动态",
      views: 980,
      featured: false,
    },
    {
      id: 3,
      title: "春季赏花季即将开启，樱花盛开迎游客",
      summary:
        "随着春天的到来，酒仙湖景区内的樱花、桃花等春花陆续绽放，为游客呈现一幅美丽的春日画卷。",
      image: imageAssets.news.news3,
      date: "2024-03-10",
      category: "季节资讯",
      views: 1580,
      featured: false,
    },
    {
      id: 4,
      title: "酒仙湖与知名旅行社达成战略合作",
      summary:
        "为拓展客源市场，酒仙湖景区与多家知名旅行社签署战略合作协议，共同推广湘东文化旅游。",
      image: imageAssets.news.news4,
      date: "2024-03-08",
      category: "合作动态",
      views: 720,
      featured: false,
    },
    {
      id: 5,
      title: "2024酒仙湖春季文化节即将盛大开幕",
      summary:
        "4月1日至4月30日，酒仙湖将举办为期一个月的春季文化节，包含民俗表演、传统手工艺展示、美食品鉴等精彩活动。",
      image: imageAssets.news.event1,
      date: "2024-03-20",
      category: "文化节庆",
      views: 2100,
      featured: true,
    },
    {
      id: 6,
      title: "宝宁寺禅修体验营开始报名",
      summary:
        "为期三天两夜的禅修体验营，让您在千年古刹中感受内心的宁静与智慧，名额有限，欢迎报名参加。",
      image: imageAssets.news.event2,
      date: "2024-03-18",
      category: "禅修活动",
      views: 890,
      featured: false,
    },
    {
      id: 7,
      title: "酒仙湖摄影大赛征稿启动",
      summary:
        '以"发现酒仙湖之美"为主题的摄影大赛正式启动，丰厚奖品等您来拿，展现您眼中的酒仙湖风采。',
      image: imageAssets.news.event3,
      date: "2024-03-16",
      category: "摄影比赛",
      views: 1350,
      featured: false,
    },
    {
      id: 8,
      title: "关于景区部分区域临时封闭的通知",
      summary:
        "因设施维护需要，白龙洞景点将于3月25日至3月30日临时封闭，给您带来的不便敬请谅解。",
      image: imageAssets.news.announcement1,
      date: "2024-03-22",
      category: "维护通知",
      views: 650,
      featured: false,
    },
    {
      id: 9,
      title: "酒仙湖景区门票价格调整公告",
      summary:
        "根据相关政策要求，自4月1日起，景区门票价格将进行适当调整，具体价格请查看官方公告。",
      image: imageAssets.news.announcement2,
      date: "2024-03-20",
      category: "价格公告",
      views: 1100,
      featured: false,
    },
  ];

  // 分类选项
  const categories = [
    { key: "all", name: "全部新闻", count: newsData.length },
    {
      key: "景区荣誉",
      name: "景区荣誉",
      count: newsData.filter((n) => n.category === "景区荣誉").length,
    },
    {
      key: "项目动态",
      name: "项目动态",
      count: newsData.filter((n) => n.category === "项目动态").length,
    },
    {
      key: "文化节庆",
      name: "文化节庆",
      count: newsData.filter((n) => n.category === "文化节庆").length,
    },
    {
      key: "季节资讯",
      name: "季节资讯",
      count: newsData.filter((n) => n.category === "季节资讯").length,
    },
    {
      key: "通知公告",
      name: "通知公告",
      count: newsData.filter(
        (n) => n.category.includes("通知") || n.category.includes("公告")
      ).length,
    },
  ];

  // 过滤新闻
  const filteredNews =
    selectedCategory === "all"
      ? newsData
      : selectedCategory === "通知公告"
      ? newsData.filter(
          (news) =>
            news.category.includes("通知") || news.category.includes("公告")
        )
      : newsData.filter((news) => news.category === selectedCategory);

  // 分页
  const totalPages = Math.ceil(filteredNews.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentNews = filteredNews.slice(startIndex, startIndex + itemsPerPage);

  // 特色新闻
  const featuredNews = newsData.filter((news) => news.featured);

  const handleNewsClick = (id: number) => {
    navigate(`/news/${id}`);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <Layout>
      {/* 页面头部 */}
      <PageHeader
        title="新闻动态"
        subtitle="了解酒仙湖最新资讯，掌握景区动态信息"
        backgroundImage={imageAssets.news.news1}
      />

      {/* 特色新闻 */}
      {featuredNews.length > 0 && (
        <section className="py-16">
          <div className="container-custom">
            <h2 className="text-2xl font-bold text-ink-black-700 mb-8">
              推荐新闻
            </h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredNews.slice(0, 2).map((news) => (
                <div
                  key={news.id}
                  className="relative group cursor-pointer"
                  onClick={() => handleNewsClick(news.id)}
                >
                  <div className="relative overflow-hidden rounded-2xl">
                    <img
                      src={news.image}
                      alt={news.title}
                      className="w-full h-80 object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                    <div className="absolute top-4 left-4">
                      <span className="bg-wine-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        {news.category}
                      </span>
                    </div>
                    <div className="absolute bottom-6 left-6 right-6 text-white">
                      <h3 className="text-xl font-bold mb-2 line-clamp-2">
                        {news.title}
                      </h3>
                      <p className="text-gray-200 text-sm line-clamp-2 mb-3">
                        {news.summary}
                      </p>
                      <div className="flex items-center justify-between text-sm">
                        <span>{formatDate(news.date)}</span>
                        <span>{news.views} 次浏览</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* 分类筛选 */}
      <section className="py-8 bg-gray-50">
        <div className="container-custom">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.key}
                onClick={() => {
                  setSelectedCategory(category.key);
                  setCurrentPage(1);
                }}
                className={cn(
                  "px-6 py-3 rounded-full font-medium transition-all duration-300",
                  selectedCategory === category.key
                    ? "bg-wine-red-600 text-white shadow-md"
                    : "bg-white text-gray-600 hover:bg-wine-red-50 hover:text-wine-red-600"
                )}
              >
                {category.name}
                <span className="ml-2 text-sm opacity-75">
                  ({category.count})
                </span>
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* 新闻列表 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentNews.map((news, index) => (
              <div
                key={news.id}
                className="animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <NewsCard
                  image={news.image}
                  title={news.title}
                  summary={news.summary}
                  date={news.date}
                  category={news.category}
                  onClick={() => handleNewsClick(news.id)}
                  className="h-full hover:shadow-strong transition-all duration-300"
                />
              </div>
            ))}
          </div>

          {currentNews.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg
                  className="w-16 h-16 mx-auto"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-600 mb-2">
                暂无相关新闻
              </h3>
              <p className="text-gray-500">请尝试其他分类或查看全部新闻</p>
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-12">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={cn(
                        "w-10 h-10 rounded-lg font-medium transition-all duration-300",
                        currentPage === page
                          ? "bg-wine-red-600 text-white"
                          : "bg-white text-gray-600 hover:bg-wine-red-50 hover:text-wine-red-600"
                      )}
                    >
                      {page}
                    </button>
                  )
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </div>
      </section>
    </Layout>
  );
};

export default News;
