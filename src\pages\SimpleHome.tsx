import React from "react";

const SimpleHome: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-800">
          酒仙湖生态旅游度假区
        </h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">欢迎来到酒仙湖</h2>
          <p className="text-gray-600 leading-relaxed">
            酒仙湖是国家4A级旅游景区，位于湖南省株洲市攸县。
            这里有美丽的湖光山色、千年古刹宝宁寺、神秘的攸女仙境等八大特色景点。
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold mb-3 text-gray-700">酒仙湖水域</h3>
            <p className="text-gray-600">总蓄水量3亿立方米，蓄水面积11.2平方公里</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold mb-3 text-gray-700">宝宁寺</h3>
            <p className="text-gray-600">佛教禅宗曹洞宗祖庭，享有"北有少林，南有宝宁"美誉</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold mb-3 text-gray-700">攸女仙境</h3>
            <p className="text-gray-600">以"攸女文化"为主题的神秘岛屿</p>
          </div>
        </div>

        <div className="mt-8 text-center">
          <div className="space-x-4">
            <a href="/attractions" className="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
              景点介绍
            </a>
            <a href="/services" className="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
              服务设施
            </a>
            <a href="/contact" className="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors">
              联系我们
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleHome;
