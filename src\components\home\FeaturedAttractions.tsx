import React, { useState } from "react";
import { cn } from "@/utils";
import { AttractionCard } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { imageAssets } from "@/utils/imageAssets";

interface FeaturedAttractionsProps {
  className?: string;
}

const FeaturedAttractions: React.FC<FeaturedAttractionsProps> = ({
  className,
}) => {
  const [activeCategory, setActiveCategory] = useState("all");

  // 景点分类
  const categories = [
    { id: "all", label: "全部景点", labelEn: "All Attractions" },
    { id: "natural", label: "自然风光", labelEn: "Natural Scenery" },
    { id: "cultural", label: "文化古迹", labelEn: "Cultural Heritage" },
    { id: "entertainment", label: "休闲娱乐", labelEn: "Entertainment" },
    { id: "wellness", label: "康养体验", labelEn: "Wellness" },
  ];

  // 特色景点数据
  const attractions = [
    {
      id: 1,
      name: "酒仙湖水域",
      nameEn: "Jiuxian Lake",
      description:
        "总蓄水量3亿立方米，蓄水面积11.2平方公里的国家级水利风景区，湖光山色美不胜收。",
      image: imageAssets.attractions.jiuxianLake,
      category: "natural",
      rating: 4.8,
      price: 68,
      tags: ["国家4A级景区", "水利风景区", "生态旅游"],
      features: ["游船观光", "湖心岛游览", "水上运动", "摄影基地"],
    },
    {
      id: 2,
      name: "宝宁寺",
      nameEn: "Baoning Temple",
      description:
        '佛教禅宗曹洞宗祖庭，享有"北有少林，南有宝宁"美誉的千年古刹。',
      image: imageAssets.attractions.baoningTemple,
      category: "cultural",
      rating: 4.9,
      price: 30,
      tags: ["佛教圣地", "禅宗祖庭", "文化遗产"],
      features: ["禅修体验", "佛教文化", "古建筑群", "祈福许愿"],
    },
    {
      id: 3,
      name: "攸女仙境",
      nameEn: "Younv Fairyland",
      description:
        '以"攸女文化"为主题的神秘岛屿，传说中攸女的居住之地，充满浪漫色彩。',
      image: imageAssets.attractions.younvFairyland,
      category: "cultural",
      rating: 4.7,
      price: 45,
      tags: ["攸女文化", "神话传说", "浪漫圣地"],
      features: ["文化体验", "传说故事", "拍照打卡", "民俗表演"],
    },
    {
      id: 4,
      name: "白龙洞",
      nameEn: "White Dragon Cave",
      description:
        "神奇的溶洞景观，洞内钟乳石千姿百态，地下河流潺潺，是探险的绝佳去处。",
      image: imageAssets.attractions.whiteDragonCave,
      category: "natural",
      rating: 4.6,
      price: 55,
      tags: ["溶洞奇观", "地质公园", "探险体验"],
      features: ["溶洞探险", "地下河流", "钟乳石群", "地质科普"],
    },
    {
      id: 5,
      name: "地质博物馆",
      nameEn: "Geological Museum",
      description:
        "展示酒埠江地质公园特色的专业博物馆，了解地球演化历史的科普基地。",
      image: imageAssets.attractions.geologicalMuseum,
      category: "cultural",
      rating: 4.5,
      price: 25,
      tags: ["地质科普", "教育基地", "科学探索"],
      features: ["地质展览", "科普教育", "互动体验", "研学旅行"],
    },
    {
      id: 6,
      name: "温泉康养中心",
      nameEn: "Hot Spring Wellness Center",
      description: "天然温泉资源，结合现代康养理念，提供身心放松的完美体验。",
      image: imageAssets.attractions.hotSpring,
      category: "wellness",
      rating: 4.8,
      price: 128,
      tags: ["温泉养生", "康养度假", "健康体验"],
      features: ["温泉洗浴", "养生保健", "瑜伽冥想", "SPA护理"],
    },
    {
      id: 7,
      name: "官田古镇",
      nameEn: "Guantian Ancient Town",
      description:
        "保存完好的湘东古镇，青石板路、古建筑群，感受浓郁的历史文化氛围。",
      image: imageAssets.attractions.guantianTown,
      category: "cultural",
      rating: 4.4,
      price: 35,
      tags: ["古镇风情", "历史文化", "民俗体验"],
      features: ["古建筑群", "民俗文化", "特色小吃", "手工艺品"],
    },
    {
      id: 8,
      name: "筏钓基地",
      nameEn: "Raft Fishing Base",
      description:
        "专业的筏钓体验基地，在湖光山色中享受垂钓的乐趣，体验渔家生活。",
      image: imageAssets.attractions.raftFishing,
      category: "entertainment",
      rating: 4.3,
      price: 88,
      tags: ["休闲垂钓", "渔家体验", "亲子活动"],
      features: ["筏钓体验", "渔具租赁", "鱼获烹饪", "亲子互动"],
    },
  ];

  // 过滤景点
  const filteredAttractions =
    activeCategory === "all"
      ? attractions
      : attractions.filter(
          (attraction) => attraction.category === activeCategory
        );

  const sectionClasses = cn("py-16 bg-rice-white-100", className);

  return (
    <section className={sectionClasses}>
      <div className="container-custom">
        {/* 标题区域 */}
        <div className="text-center mb-12">
          <div className="inline-block bg-lake-green-100 text-lake-green-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            特色景点
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-ink-black-700 mb-4 brush-text">
            探索酒仙湖之美
          </h2>
          <p className="text-lg text-ink-black-500 max-w-2xl mx-auto leading-relaxed">
            从自然风光到文化古迹，从休闲娱乐到康养体验，酒仙湖为您呈现多元化的旅游体验
          </p>
        </div>

        {/* 分类筛选 */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={cn(
                "px-6 py-3 rounded-full font-medium transition-all duration-300",
                {
                  "bg-lake-green-500 text-white shadow-md":
                    activeCategory === category.id,
                  "bg-white text-ink-black-600 hover:bg-lake-green-50 hover:text-lake-green-600 border border-gray-200":
                    activeCategory !== category.id,
                }
              )}
            >
              {category.label}
            </button>
          ))}
        </div>

        {/* 景点网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {filteredAttractions.map((attraction, index) => (
            <div
              key={attraction.id}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <AttractionCard
                image={attraction.image}
                title={attraction.name}
                description={attraction.description}
                rating={attraction.rating}
                price={attraction.price}
                onClick={() => {
                  // 跳转到景点详情页
                  window.location.href = `/attractions/${attraction.id}`;
                }}
                className="h-full"
              />
            </div>
          ))}
        </div>

        {/* 统计信息 */}
        <div className="bg-white rounded-2xl shadow-soft p-8 mb-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-lake-green-500 mb-2">
                8+
              </div>
              <div className="text-ink-black-600">核心景点</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-lake-green-500 mb-2">
                11.2
              </div>
              <div className="text-ink-black-600">平方公里水域</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-lake-green-500 mb-2">
                4A
              </div>
              <div className="text-ink-black-600">国家级景区</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-lake-green-500 mb-2">
                1000+
              </div>
              <div className="text-ink-black-600">年历史文化</div>
            </div>
          </div>
        </div>

        {/* 行动号召 */}
        <div className="text-center">
          <div className="bg-gradient-lake rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">开启您的酒仙湖之旅</h3>
            <p className="text-lg mb-6 opacity-90">
              预订门票，享受专属优惠，体验湘东明珠的独特魅力
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                className="bg-white text-lake-green-600 hover:bg-gray-100"
              >
                立即预订门票
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-lake-green-600"
              >
                查看更多景点
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedAttractions;
