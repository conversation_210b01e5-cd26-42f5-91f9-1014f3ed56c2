import React, { useState } from "react";
import Layout from "@/components/layout/Layout";
import { cn } from "@/utils";
import { imageAssets } from "@/utils/imageAssets";
import FallbackImage from "@/components/ui/FallbackImage";
import Card from "@/components/ui/Card";
import { BookingButton } from "@/components/ui/Button";
import PageHeader from "@/components/ui/PageHeader";

interface Service {
  id: string;
  name: string;
  description: string;
  image: string;
  features: string[];
  price?: string;
  contact?: string;
  category: string;
}

const Services: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  // 服务数据
  const services: Service[] = [
    {
      id: "accommodation",
      name: "住宿服务",
      description:
        "提供多样化的住宿选择，从湖景客房到特色民宿，满足不同需求的游客",
      image: imageAssets.services.accommodation,
      features: [
        "湖景客房，推窗见湖",
        "特色民宿，体验当地文化",
        "商务套房，设施齐全",
        "24小时前台服务",
        "免费WiFi覆盖",
        "停车场免费使用",
      ],
      price: "¥288起/晚",
      contact: "0731-24688888",
      category: "accommodation",
    },
    {
      id: "dining",
      name: "餐饮服务",
      description: "汇聚湘菜精华与地方特色，为您呈现舌尖上的酒仙湖美食体验",
      image: imageAssets.services.dining,
      features: [
        "湘菜经典，地道口味",
        "湖鲜特色，新鲜美味",
        "素食餐厅，健康养生",
        "包厢服务，私密用餐",
        "宴会定制，专业服务",
        "外卖配送，便民服务",
      ],
      price: "¥68起/人",
      contact: "0731-24688889",
      category: "dining",
    },
    {
      id: "entertainment",
      name: "娱乐项目",
      description: "丰富多彩的娱乐活动，让您的酒仙湖之旅充满乐趣和惊喜",
      image: imageAssets.services.entertainment,
      features: [
        "游船观光，湖光山色",
        "筏钓体验，休闲垂钓",
        "水上运动，激情体验",
        "KTV包厢，欢歌笑语",
        "棋牌室，休闲娱乐",
        "儿童乐园，亲子时光",
      ],
      price: "¥50起/项",
      contact: "0731-24688890",
      category: "entertainment",
    },
    {
      id: "wellness",
      name: "康养服务",
      description:
        "结合天然温泉资源，提供专业的康养保健服务，让身心得到完全放松",
      image: imageAssets.attractions.hotSpring,
      features: [
        "天然温泉，养生保健",
        "SPA护理，专业服务",
        "瑜伽冥想，身心平衡",
        "中医理疗，传统养生",
        "健身房，运动健康",
        "营养餐饮，科学搭配",
      ],
      price: "¥188起/次",
      contact: "0731-24688891",
      category: "wellness",
    },
    {
      id: "conference",
      name: "会议服务",
      description: "专业的会议设施和服务团队，为您的商务活动提供完美保障",
      image: imageAssets.services.serviceHighlight,
      features: [
        "多功能会议厅，设备先进",
        "同声传译，专业服务",
        "茶歇服务，贴心安排",
        "商务套餐，一站式服务",
        "接送服务，便捷出行",
        "活动策划，专业团队",
      ],
      price: "¥1888起/天",
      contact: "0731-24688892",
      category: "business",
    },
    {
      id: "transportation",
      name: "交通服务",
      description: "便捷的交通接驳服务，让您的出行更加轻松舒适",
      image: imageAssets.backgrounds.sectionBg1,
      features: [
        "机场接送，专车服务",
        "高铁站接送，准时到达",
        "景区巴士，定时发车",
        "租车服务，自由出行",
        "导游服务，专业讲解",
        "行李寄存，贴心服务",
      ],
      price: "¥80起/次",
      contact: "0731-24688893",
      category: "transportation",
    },
  ];

  // 分类选项
  const categories = [
    { key: "all", name: "全部服务" },
    { key: "accommodation", name: "住宿服务" },
    { key: "dining", name: "餐饮服务" },
    { key: "entertainment", name: "娱乐项目" },
    { key: "wellness", name: "康养服务" },
    { key: "business", name: "商务服务" },
    { key: "transportation", name: "交通服务" },
  ];

  // 过滤服务
  const filteredServices =
    selectedCategory === "all"
      ? services
      : services.filter((service) => service.category === selectedCategory);

  return (
    <Layout>
      {/* 页面头部 */}
      <PageHeader
        title="服务设施"
        subtitle="全方位的服务体系，为您的酒仙湖之旅提供完美保障"
        backgroundImage={imageAssets.services.hotel}
      />

      {/* 服务概览 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ink-black-700 mb-4">
              服务概览
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              酒仙湖致力于为每一位游客提供优质、贴心的服务体验。
              从住宿餐饮到娱乐康养，从商务会议到交通接驳，我们的专业团队随时为您服务。
            </p>
          </div>

          {/* 服务统计 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            <div className="text-center">
              <div className="text-3xl font-bold text-lake-green-600 mb-2">
                6大
              </div>
              <div className="text-gray-600">服务类别</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-lake-green-600 mb-2">
                24小时
              </div>
              <div className="text-gray-600">贴心服务</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-lake-green-600 mb-2">
                100+
              </div>
              <div className="text-gray-600">服务人员</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-lake-green-600 mb-2">
                98%
              </div>
              <div className="text-gray-600">满意度</div>
            </div>
          </div>
        </div>
      </section>

      {/* 分类筛选 */}
      <section className="py-8 bg-gray-50">
        <div className="container-custom">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.key}
                onClick={() => setSelectedCategory(category.key)}
                className={cn(
                  "px-6 py-3 rounded-full font-medium transition-all duration-300",
                  selectedCategory === category.key
                    ? "bg-elegant-gold-400 text-ink-black-700 shadow-md"
                    : "bg-white text-gray-600 hover:bg-elegant-gold-50 hover:text-elegant-gold-600"
                )}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* 服务列表 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {filteredServices.map((service, index) => (
              <Card
                key={service.id}
                className="overflow-hidden hover:shadow-strong transition-all duration-300 animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="md:flex">
                  <div className="md:w-1/3">
                    <FallbackImage
                      src={service.image}
                      alt={service.name}
                      className="w-full h-48 md:h-full object-cover"
                      fallbackType="pattern"
                    />
                  </div>
                  <div className="md:w-2/3 p-6">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-xl font-semibold text-ink-black-700">
                        {service.name}
                      </h3>
                      {service.price && (
                        <span className="text-lg font-bold text-elegant-gold-600">
                          {service.price}
                        </span>
                      )}
                    </div>

                    <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                      {service.description}
                    </p>

                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-ink-black-700 mb-2">
                        服务特色：
                      </h4>
                      <div className="grid grid-cols-1 gap-1">
                        {service.features.slice(0, 4).map((feature, idx) => (
                          <div
                            key={idx}
                            className="flex items-center gap-2 text-sm text-gray-600"
                          >
                            <div className="w-1 h-1 bg-elegant-gold-400 rounded-full flex-shrink-0" />
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      {service.contact && (
                        <div className="text-sm text-gray-500">
                          咨询电话：{service.contact}
                        </div>
                      )}
                      <BookingButton size="sm">立即预订</BookingButton>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* 服务承诺 */}
      <section className="py-16 bg-gradient-to-r from-lake-green-50 to-elegant-gold-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ink-black-700 mb-4">
              服务承诺
            </h2>
            <p className="text-lg text-gray-600">我们的承诺，您的保障</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-lake-green-400 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🛡️</span>
              </div>
              <h3 className="text-xl font-semibold text-ink-black-700 mb-2">
                品质保证
              </h3>
              <p className="text-gray-600">
                严格的服务标准，确保每一项服务都达到优质水准
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-elegant-gold-400 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">⏰</span>
              </div>
              <h3 className="text-xl font-semibold text-ink-black-700 mb-2">
                及时响应
              </h3>
              <p className="text-gray-600">
                24小时客服热线，随时为您解决问题和需求
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-wine-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">💝</span>
              </div>
              <h3 className="text-xl font-semibold text-ink-black-700 mb-2">
                贴心服务
              </h3>
              <p className="text-gray-600">
                个性化定制服务，满足您的特殊需求和偏好
              </p>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Services;
