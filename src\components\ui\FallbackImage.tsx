import React, { useState } from 'react';
import { cn } from '@/utils';

interface FallbackImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  fallbackType?: 'placeholder' | 'gradient' | 'pattern';
  onLoad?: () => void;
  onError?: () => void;
}

const FallbackImage: React.FC<FallbackImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  fallbackType = 'gradient',
  onLoad,
  onError,
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    onError?.();
  };

  const getFallbackContent = () => {
    const baseClasses = cn(
      'flex items-center justify-center text-white',
      className
    );

    switch (fallbackType) {
      case 'placeholder':
        return (
          <div className={cn(baseClasses, 'bg-gray-300')}>
            <div className="text-center">
              <svg className="w-12 h-12 mx-auto mb-2 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
              <p className="text-sm text-gray-500">图片加载中...</p>
            </div>
          </div>
        );
      
      case 'pattern':
        return (
          <div className={cn(baseClasses, 'bg-gradient-to-br from-lake-green-400 to-lake-green-600 relative overflow-hidden')}>
            <div className="absolute inset-0 opacity-20">
              <svg width="100%" height="100%" viewBox="0 0 100 100" className="w-full h-full">
                <defs>
                  <pattern id="waves" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M0 10C5 5, 15 15, 20 10" stroke="white" strokeWidth="1" fill="none" opacity="0.3"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#waves)" />
              </svg>
            </div>
            <div className="relative z-10 text-center">
              <h3 className="text-lg font-bold mb-1">酒仙湖</h3>
              <p className="text-sm opacity-90">{alt}</p>
            </div>
          </div>
        );
      
      case 'gradient':
      default:
        return (
          <div className={cn(baseClasses, 'bg-gradient-to-br from-lake-green-400 via-elegant-gold-400 to-lake-green-500')}>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-3 bg-white/20 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" />
                </svg>
              </div>
              <p className="text-sm font-medium">{alt}</p>
            </div>
          </div>
        );
    }
  };

  if (hasError) {
    return getFallbackContent();
  }

  return (
    <div className="relative">
      {isLoading && getFallbackContent()}
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          className,
          isLoading ? 'absolute inset-0 opacity-0' : 'opacity-100',
          'transition-opacity duration-300'
        )}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          aspectRatio: width && height ? `${width}/${height}` : undefined,
        }}
      />
    </div>
  );
};

export default FallbackImage;
