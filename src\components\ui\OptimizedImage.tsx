import React, { useState, useEffect } from "react";
import { cn, mediaUtils } from "@/utils";
import FallbackImage from "./FallbackImage";

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  lazy?: boolean;
  responsive?: boolean;
  placeholder?: "blur" | "empty";
  blurDataURL?: string;
  sizes?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  priority = false,
  lazy = true,
  responsive = true,
  placeholder = "blur",
  blurDataURL,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>("");

  // 生成响应式图片源
  const generateSources = () => {
    if (!responsive) return { src, srcSet: "" };

    try {
      const srcSet = mediaUtils.generateSrcSet(src);
      return { src, srcSet };
    } catch {
      return { src, srcSet: "" };
    }
  };

  const { src: imageSrc, srcSet } = generateSources();

  // 处理图片加载
  useEffect(() => {
    if (!lazy || priority) {
      setCurrentSrc(imageSrc);
      return;
    }

    // 懒加载逻辑
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setCurrentSrc(imageSrc);
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    const imageElement = document.querySelector(`[data-src="${imageSrc}"]`);
    if (imageElement) {
      observer.observe(imageElement);
    }

    return () => observer.disconnect();
  }, [imageSrc, lazy, priority]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setIsError(true);
    onError?.();
  };

  // 默认模糊占位符
  const defaultBlurDataURL =
    "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=";

  const imageClasses = cn(
    "transition-opacity duration-300",
    {
      "opacity-0": !isLoaded && !isError,
      "opacity-100": isLoaded,
    },
    className
  );

  const containerClasses = cn("relative overflow-hidden", {
    "bg-gray-200": placeholder === "blur" && !isLoaded,
  });

  // 错误状态
  if (isError) {
    return (
      <div
        className={cn(
          containerClasses,
          "flex items-center justify-center bg-gray-100",
          className
        )}
      >
        <div className="text-center text-gray-400">
          <svg
            className="w-8 h-8 mx-auto mb-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <span className="text-xs">图片加载失败</span>
        </div>
      </div>
    );
  }

  return (
    <div className={containerClasses}>
      {/* 模糊占位符 */}
      {placeholder === "blur" && !isLoaded && (
        <img
          src={blurDataURL || defaultBlurDataURL}
          alt=""
          className={cn(
            "absolute inset-0 w-full h-full object-cover filter blur-sm scale-110",
            "transition-opacity duration-300",
            { "opacity-100": !isLoaded, "opacity-0": isLoaded }
          )}
          aria-hidden="true"
        />
      )}

      {/* 加载动画 */}
      {!isLoaded && placeholder !== "blur" && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-lake-green-500"></div>
        </div>
      )}

      {/* 主图片 */}
      <img
        src={currentSrc || (lazy ? "" : imageSrc)}
        srcSet={responsive ? srcSet : undefined}
        sizes={responsive ? sizes : undefined}
        alt={alt}
        width={width}
        height={height}
        className={imageClasses}
        loading={lazy && !priority ? "lazy" : "eager"}
        data-src={lazy ? imageSrc : undefined}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          aspectRatio: width && height ? `${width}/${height}` : undefined,
        }}
      />
    </div>
  );
};

// 预设图片组件
export const HeroImage: React.FC<
  Omit<OptimizedImageProps, "priority" | "lazy">
> = (props) => <OptimizedImage priority lazy={false} {...props} />;

export const LazyImage: React.FC<Omit<OptimizedImageProps, "lazy">> = (
  props
) => <OptimizedImage lazy {...props} />;

export const ResponsiveImage: React.FC<
  Omit<OptimizedImageProps, "responsive">
> = (props) => <OptimizedImage responsive {...props} />;

// 特定用途的图片组件
export const AttractionImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
}> = ({ src, alt, className }) => (
  <FallbackImage
    src={src}
    alt={alt}
    className={cn("w-full h-48 object-cover", className)}
    width={800}
    height={600}
    fallbackType="pattern"
  />
);

export const NewsImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
}> = ({ src, alt, className }) => (
  <OptimizedImage
    src={src}
    alt={alt}
    className={cn("w-full h-40 object-cover", className)}
    width={600}
    height={400}
    responsive
    lazy
    placeholder="blur"
  />
);

export const AvatarImage: React.FC<{
  src: string;
  alt: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}> = ({ src, alt, size = "md", className }) => {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12",
    lg: "w-16 h-16",
  };

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={cn("rounded-full object-cover", sizeClasses[size], className)}
      width={size === "sm" ? 32 : size === "md" ? 48 : 64}
      height={size === "sm" ? 32 : size === "md" ? 48 : 64}
      lazy
      placeholder="blur"
    />
  );
};

export default OptimizedImage;
