// 图片资源配置文件
// 使用 Unsplash 等高质量图片资源

export interface ImageAsset {
  id: string;
  url: string;
  alt: string;
  width?: number;
  height?: number;
  category: string;
  description?: string;
}

// Unsplash 图片配置
const UNSPLASH_BASE = "https://images.unsplash.com";

// 酒仙湖相关的高质量图片资源
export const imageAssets = {
  // Logo 相关
  logo: {
    main: "/src/assets/images/logo/logo.svg",
    white: "/src/assets/images/logo/logo-white.svg",
    favicon: "/images/logo/favicon.ico",
  },

  // 首页轮播图 - 使用湖泊、山水、寺庙等主题的高质量图片
  hero: {
    slide1: `${UNSPLASH_BASE}/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop&crop=center`, // 美丽湖泊
    slide2: `${UNSPLASH_BASE}/photo-1548013146-72479768bada?w=1920&h=1080&fit=crop&crop=center`, // 古寺庙
    slide3: `${UNSPLASH_BASE}/photo-1544551763-46a013bb70d5?w=1920&h=1080&fit=crop&crop=center`, // 山水风景
    slide4: `${UNSPLASH_BASE}/photo-1571019613454-1cb2f99b2d8b?w=1920&h=1080&fit=crop&crop=center`, // 温泉度假
  },

  // 景点图片 - 精选与酒仙湖景点相关的图片
  attractions: {
    jiuxianLake: `${UNSPLASH_BASE}/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center`, // 酒仙湖主景
    baoningTemple: `${UNSPLASH_BASE}/photo-1548013146-72479768bada?w=800&h=600&fit=crop&crop=center`, // 宝宁寺
    younvFairyland: `${UNSPLASH_BASE}/photo-1544551763-46a013bb70d5?w=800&h=600&fit=crop&crop=center`, // 攸女仙境
    whiteDragonCave: `${UNSPLASH_BASE}/photo-1551632811-561732d1e306?w=800&h=600&fit=crop&crop=center`, // 白龙洞
    geologicalMuseum: `${UNSPLASH_BASE}/photo-1581833971358-2c8b550f87b3?w=800&h=600&fit=crop&crop=center`, // 地质博物馆
    hotSpring: `${UNSPLASH_BASE}/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center`, // 温泉
    guantianTown: `${UNSPLASH_BASE}/photo-1547036967-23d11aacaee0?w=800&h=600&fit=crop&crop=center`, // 官田古镇
    raftFishing: `${UNSPLASH_BASE}/photo-1544551763-77ef2d0cfc6c?w=800&h=600&fit=crop&crop=center`, // 筏钓基地
  },

  // 新闻图片
  news: {
    news1: `${UNSPLASH_BASE}/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop&crop=center`, // 生态旅游
    news2: `${UNSPLASH_BASE}/photo-1581833971358-2c8b550f87b3?w=600&h=400&fit=crop&crop=center`, // 数字化升级
    news3: `${UNSPLASH_BASE}/photo-1490750967868-88aa4486c946?w=600&h=400&fit=crop&crop=center`, // 春季赏花
    news4: `${UNSPLASH_BASE}/photo-1544551763-46a013bb70d5?w=600&h=400&fit=crop&crop=center`, // 其他新闻
    event1: `${UNSPLASH_BASE}/photo-1492684223066-81342ee5ff30?w=600&h=400&fit=crop&crop=center`, // 活动图片
    event2: `${UNSPLASH_BASE}/photo-1547036967-23d11aacaee0?w=600&h=400&fit=crop&crop=center`, // 活动图片
    event3: `${UNSPLASH_BASE}/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop&crop=center`, // 活动图片
    announcement1: `${UNSPLASH_BASE}/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop&crop=center`, // 公告图片
    announcement2: `${UNSPLASH_BASE}/photo-1544551763-46a013bb70d5?w=600&h=400&fit=crop&crop=center`, // 公告图片
  },

  // 服务相关图片
  services: {
    serviceHighlight: `${UNSPLASH_BASE}/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center`, // 服务亮点
    accommodation: `${UNSPLASH_BASE}/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop&crop=center`, // 住宿服务
    dining: `${UNSPLASH_BASE}/photo-1414235077428-338989a2e8c0?w=800&h=600&fit=crop&crop=center`, // 餐饮服务
    entertainment: `${UNSPLASH_BASE}/photo-1492684223066-81342ee5ff30?w=800&h=600&fit=crop&crop=center`, // 娱乐服务
  },

  // 背景图片
  backgrounds: {
    sectionBg1: `${UNSPLASH_BASE}/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop&crop=center&blur=20`, // 模糊背景1
    sectionBg2: `${UNSPLASH_BASE}/photo-1544551763-46a013bb70d5?w=1920&h=1080&fit=crop&crop=center&blur=20`, // 模糊背景2
    pattern: "/images/backgrounds/pattern.png", // 装饰图案
  },

  // 用户头像
  avatars: {
    avatar1: `${UNSPLASH_BASE}/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face`, // 用户头像1
    avatar2: `${UNSPLASH_BASE}/photo-1494790108755-2616c6d4e6e8?w=100&h=100&fit=crop&crop=face`, // 用户头像2
    avatar3: `${UNSPLASH_BASE}/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face`, // 用户头像3
  },
};

// 图片路径映射函数
export const getImageUrl = (
  category: keyof typeof imageAssets,
  key: string
): string => {
  const categoryImages = imageAssets[category] as Record<string, string>;
  return categoryImages[key] || "";
};

// 获取响应式图片URL
export const getResponsiveImageUrl = (
  baseUrl: string,
  width: number,
  height?: number
): string => {
  if (baseUrl.includes("unsplash.com")) {
    const url = new URL(baseUrl);
    url.searchParams.set("w", width.toString());
    if (height) {
      url.searchParams.set("h", height.toString());
    }
    return url.toString();
  }
  return baseUrl;
};

// 预加载关键图片
export const preloadImages = (urls: string[]): Promise<void[]> => {
  return Promise.all(
    urls.map(
      (url) =>
        new Promise<void>((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve();
          img.onerror = reject;
          img.src = url;
        })
    )
  );
};

// 图片懒加载配置
export const lazyLoadConfig = {
  threshold: 0.1,
  rootMargin: "50px",
};
