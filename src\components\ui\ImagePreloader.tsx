import React, { useEffect, useState } from 'react';
import { imageAssets, preloadImages } from '@/utils/imageAssets';

interface ImagePreloaderProps {
  children: React.ReactNode;
}

const ImagePreloader: React.FC<ImagePreloaderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [loadedCount, setLoadedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    // 收集所有需要预加载的关键图片
    const criticalImages = [
      imageAssets.hero.slide1,
      imageAssets.hero.slide2,
      imageAssets.hero.slide3,
      imageAssets.hero.slide4,
      imageAssets.attractions.jiuxianLake,
      imageAssets.attractions.baoningTemple,
      imageAssets.attractions.younvFairyland,
      imageAssets.news.news1,
      imageAssets.news.news2,
      imageAssets.news.news3,
    ];

    setTotalCount(criticalImages.length);

    // 预加载图片
    const loadImages = async () => {
      let loaded = 0;
      
      for (const imageUrl of criticalImages) {
        try {
          await new Promise<void>((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
              loaded++;
              setLoadedCount(loaded);
              resolve();
            };
            img.onerror = () => {
              loaded++;
              setLoadedCount(loaded);
              resolve(); // 即使加载失败也继续
            };
            img.src = imageUrl;
          });
        } catch (error) {
          console.warn('Failed to load image:', imageUrl);
        }
      }
      
      // 添加一个小延迟确保用户能看到加载过程
      setTimeout(() => {
        setIsLoading(false);
      }, 500);
    };

    loadImages();
  }, []);

  if (isLoading) {
    const progress = totalCount > 0 ? (loadedCount / totalCount) * 100 : 0;
    
    return (
      <div className="fixed inset-0 bg-rice-white-100 flex items-center justify-center z-50">
        <div className="text-center">
          {/* Logo */}
          <div className="mb-8">
            <img 
              src={imageAssets.logo.main} 
              alt="酒仙湖" 
              className="w-48 h-auto mx-auto"
            />
          </div>
          
          {/* 加载进度 */}
          <div className="w-64 mx-auto mb-4">
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <div 
                className="bg-gradient-to-r from-lake-green-400 to-lake-green-500 h-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
          
          {/* 加载文字 */}
          <p className="text-ink-black-600 text-sm">
            正在加载精美图片... {Math.round(progress)}%
          </p>
          
          {/* 加载动画 */}
          <div className="mt-6 flex justify-center space-x-1">
            <div className="w-2 h-2 bg-lake-green-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-lake-green-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-2 h-2 bg-lake-green-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default ImagePreloader;
