import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { cn } from '@/utils';
import { imageAssets } from '@/utils/imageAssets';
import FallbackImage from '@/components/ui/FallbackImage';
import { BookingButton } from '@/components/ui/Button';
import Button from '@/components/ui/Button';

interface AttractionData {
  id: number;
  name: string;
  nameEn: string;
  description: string;
  detailedDescription: string;
  image: string;
  gallery: string[];
  category: string;
  rating: number;
  price: number;
  tags: string[];
  features: string[];
  location: string;
  openTime: string;
  duration: string;
  highlights: string[];
  facilities: string[];
  tips: string[];
  history?: string;
  culture?: string;
}

const AttractionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [attraction, setAttraction] = useState<AttractionData | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loading, setLoading] = useState(true);

  // 景点详细数据
  const attractionsData: Record<string, AttractionData> = {
    '1': {
      id: 1,
      name: '酒仙湖水域',
      nameEn: 'Jiuxian Lake',
      description: '总蓄水量3亿立方米，蓄水面积11.2平方公里的国家级水利风景区',
      detailedDescription: '酒仙湖是酒埠江国家地质公园的核心景区，湖面宽阔，水质清澈，四周群山环抱，形成了独特的高峡平湖景观。湖中有大小岛屿十余个，其中湖心岛是最大的岛屿，岛上植被茂密，是观光游览的绝佳去处。',
      image: imageAssets.attractions.jiuxianLake,
      gallery: [
        imageAssets.attractions.jiuxianLake,
        imageAssets.hero.slide1,
        imageAssets.backgrounds.sectionBg1
      ],
      category: 'natural',
      rating: 4.8,
      price: 68,
      tags: ['国家4A级景区', '水利风景区', '生态旅游'],
      features: ['游船观光', '湖心岛游览', '水上运动', '摄影基地'],
      location: '湖南省株洲市攸县酒埠江镇',
      openTime: '08:00-18:00',
      duration: '3-4小时',
      highlights: [
        '高峡平湖的壮美景观',
        '清澈见底的湖水',
        '神秘的湖心岛',
        '丰富的水上活动'
      ],
      facilities: ['游客中心', '停车场', '游船码头', '观景台', '休息亭'],
      tips: [
        '建议乘坐游船游览，可以更好地欣赏湖光山色',
        '湖心岛上有步行道，适合徒步游览',
        '最佳拍照时间为早晨和傍晚',
        '夏季注意防晒，冬季注意保暖'
      ],
      history: '酒仙湖形成于1958年，是因修建酒埠江水库而形成的人工湖泊。湖名来源于当地的酒仙传说，相传古时有仙人在此酿酒，故名酒仙湖。',
      culture: '酒仙湖承载着深厚的湖湘文化底蕴，湖区内有多处古迹遗址，体现了攸县悠久的历史文化传统。'
    },
    '2': {
      id: 2,
      name: '宝宁寺',
      nameEn: 'Baoning Temple',
      description: '佛教禅宗曹洞宗祖庭，享有"北有少林，南有宝宁"美誉',
      detailedDescription: '宝宁寺始建于唐代，是佛教禅宗曹洞宗的重要祖庭之一。寺院建筑宏伟，古朴典雅，保存了大量珍贵的佛教文物和古建筑。寺内有大雄宝殿、观音殿、藏经楼等主要建筑，是研究中国佛教建筑艺术的重要实物资料。',
      image: imageAssets.attractions.baoningTemple,
      gallery: [
        imageAssets.attractions.baoningTemple,
        imageAssets.hero.slide2,
        imageAssets.news.event2
      ],
      category: 'cultural',
      rating: 4.9,
      price: 30,
      tags: ['佛教圣地', '禅宗祖庭', '文化遗产'],
      features: ['禅修体验', '佛教文化', '古建筑群', '祈福许愿'],
      location: '酒仙湖景区内',
      openTime: '06:00-18:00',
      duration: '2-3小时',
      highlights: [
        '千年古刹的庄严肃穆',
        '精美的佛教建筑艺术',
        '深厚的禅宗文化底蕴',
        '宁静的修行环境'
      ],
      facilities: ['山门', '大雄宝殿', '观音殿', '藏经楼', '禅修堂', '素食餐厅'],
      tips: [
        '进入寺院请保持安静，尊重宗教礼仪',
        '可以参加早晚课诵，体验佛教文化',
        '寺内提供素食，口味清淡营养丰富',
        '建议穿着得体，避免过于暴露的服装'
      ],
      history: '宝宁寺始建于唐代贞观年间，历经宋、元、明、清各朝修缮扩建，形成了今天的规模。寺院曾是湘南地区重要的佛教中心。',
      culture: '宝宁寺是禅宗曹洞宗的重要道场，在中国佛教史上占有重要地位。寺院保存了丰富的佛教文化遗产，是研究中国佛教文化的重要场所。'
    }
    // 其他景点数据将在后续添加
  };

  useEffect(() => {
    if (id && attractionsData[id]) {
      setAttraction(attractionsData[id]);
    } else {
      // 如果景点不存在，重定向到景点列表页
      navigate('/attractions');
    }
    setLoading(false);
  }, [id, navigate]);

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lake-green-500"></div>
        </div>
      </Layout>
    );
  }

  if (!attraction) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-ink-black-700 mb-4">景点不存在</h1>
            <Button onClick={() => navigate('/attractions')}>返回景点列表</Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* 英雄区域 */}
      <section className="relative h-96 overflow-hidden">
        <FallbackImage
          src={attraction.gallery[currentImageIndex]}
          alt={attraction.name}
          className="w-full h-full object-cover"
          fallbackType="gradient"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
        
        {/* 内容 */}
        <div className="absolute inset-0 flex items-end">
          <div className="container-custom pb-12">
            <div className="text-white">
              <div className="flex items-center gap-2 mb-4">
                <span className="bg-elegant-gold-400 text-ink-black-700 px-3 py-1 rounded-full text-sm font-medium">
                  {attraction.category === 'natural' ? '自然景观' : 
                   attraction.category === 'cultural' ? '文化景观' : 
                   attraction.category === 'wellness' ? '康养度假' : '娱乐体验'}
                </span>
                <div className="flex items-center gap-1">
                  <span className="text-yellow-400">★</span>
                  <span className="text-sm">{attraction.rating}</span>
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-2 brush-text">{attraction.name}</h1>
              <p className="text-xl text-gray-200 mb-4">{attraction.nameEn}</p>
              <p className="text-lg max-w-2xl">{attraction.description}</p>
            </div>
          </div>
        </div>

        {/* 图片导航 */}
        {attraction.gallery.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="flex gap-2">
              {attraction.gallery.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={cn(
                    'w-3 h-3 rounded-full transition-all duration-300',
                    currentImageIndex === index ? 'bg-white' : 'bg-white/50'
                  )}
                />
              ))}
            </div>
          </div>
        )}
      </section>

      {/* 主要内容 */}
      <section className="py-16">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* 左侧内容 */}
            <div className="lg:col-span-2 space-y-8">
              {/* 详细介绍 */}
              <div>
                <h2 className="text-2xl font-bold text-ink-black-700 mb-4">景点介绍</h2>
                <p className="text-gray-600 leading-relaxed">{attraction.detailedDescription}</p>
              </div>

              {/* 亮点特色 */}
              <div>
                <h3 className="text-xl font-semibold text-ink-black-700 mb-4">景点亮点</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {attraction.highlights.map((highlight, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-lake-green-400 rounded-full mt-2 flex-shrink-0" />
                      <span className="text-gray-600">{highlight}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 历史文化 */}
              {(attraction.history || attraction.culture) && (
                <div className="space-y-6">
                  {attraction.history && (
                    <div>
                      <h3 className="text-xl font-semibold text-ink-black-700 mb-4">历史沿革</h3>
                      <p className="text-gray-600 leading-relaxed">{attraction.history}</p>
                    </div>
                  )}
                  {attraction.culture && (
                    <div>
                      <h3 className="text-xl font-semibold text-ink-black-700 mb-4">文化内涵</h3>
                      <p className="text-gray-600 leading-relaxed">{attraction.culture}</p>
                    </div>
                  )}
                </div>
              )}

              {/* 游览贴士 */}
              <div>
                <h3 className="text-xl font-semibold text-ink-black-700 mb-4">游览贴士</h3>
                <div className="space-y-3">
                  {attraction.tips.map((tip, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-lake-green-50 rounded-lg">
                      <div className="w-5 h-5 bg-lake-green-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs">💡</span>
                      </div>
                      <span className="text-gray-700">{tip}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 右侧信息卡 */}
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="bg-white rounded-xl shadow-soft p-6">
                <h3 className="text-lg font-semibold text-ink-black-700 mb-4">基本信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">门票价格</span>
                    <span className="font-semibold text-lake-green-600">¥{attraction.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">开放时间</span>
                    <span className="text-ink-black-600">{attraction.openTime}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">建议游览</span>
                    <span className="text-ink-black-600">{attraction.duration}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">景点位置</span>
                    <span className="text-ink-black-600 text-right text-sm">{attraction.location}</span>
                  </div>
                </div>
                
                <div className="mt-6">
                  <BookingButton className="w-full">立即预订</BookingButton>
                </div>
              </div>

              {/* 特色标签 */}
              <div className="bg-white rounded-xl shadow-soft p-6">
                <h3 className="text-lg font-semibold text-ink-black-700 mb-4">特色标签</h3>
                <div className="flex flex-wrap gap-2">
                  {attraction.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-lake-green-100 text-lake-green-700 px-3 py-1 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              {/* 服务设施 */}
              <div className="bg-white rounded-xl shadow-soft p-6">
                <h3 className="text-lg font-semibold text-ink-black-700 mb-4">服务设施</h3>
                <div className="space-y-2">
                  {attraction.facilities.map((facility, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-elegant-gold-400 rounded-full" />
                      <span className="text-gray-600 text-sm">{facility}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default AttractionDetail;
