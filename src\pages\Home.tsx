import React from "react";
import Layout from "@/components/layout/Layout";
import HeroSection from "@/components/home/<USER>";
import FeaturedAttractions from "@/components/home/<USER>";
import ServicesSection from "@/components/home/<USER>";
import NewsSection from "@/components/home/<USER>";

const Home: React.FC = () => {
  return (
    <Layout>
      {/* 英雄区域 */}
      <HeroSection />

      {/* 特色景点 */}
      <FeaturedAttractions />

      {/* 服务展示 */}
      <ServicesSection />

      {/* 新闻动态 */}
      <NewsSection />

      {/* 开发测试链接 */}
      {process.env.NODE_ENV === "development" && (
        <div className="fixed bottom-4 right-4 z-50">
          <a
            href="/image-test"
            className="bg-wine-red-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-wine-red-700 transition-colors text-sm"
          >
            图片测试
          </a>
        </div>
      )}
    </Layout>
  );
};

export default Home;
