import React, { useState } from "react";
import { cn, formatDate } from "@/utils";
import { NewsCard } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { imageAssets } from "@/utils/imageAssets";

interface NewsSectionProps {
  className?: string;
}

const NewsSection: React.FC<NewsSectionProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState("news");

  // 新闻分类
  const tabs = [
    { id: "news", label: "景区新闻", icon: "📰" },
    { id: "events", label: "活动公告", icon: "🎉" },
    { id: "announcements", label: "重要通知", icon: "📢" },
  ];

  // 新闻数据
  const newsData = {
    news: [
      {
        id: 1,
        title: '酒仙湖景区荣获"湖南省生态旅游示范区"称号',
        summary:
          "近日，湖南省文化和旅游厅公布了2024年度生态旅游示范区名单，酒仙湖景区凭借其优美的自然环境和完善的生态保护措施成功入选。",
        image: imageAssets.news.news1,
        date: "2024-03-15",
        category: "景区荣誉",
        views: 1250,
        featured: true,
      },
      {
        id: 2,
        title: "酒仙湖数字化升级项目正式启动",
        summary:
          "为提升游客体验，酒仙湖景区启动全面数字化升级项目，将引入VR全景游览、AR互动体验等先进技术。",
        image: imageAssets.news.news2,
        date: "2024-03-12",
        category: "项目动态",
        views: 980,
        featured: false,
      },
      {
        id: 3,
        title: "春季赏花季即将开启，樱花盛开迎游客",
        summary:
          "随着春天的到来，酒仙湖景区内的樱花、桃花等春花陆续绽放，为游客呈现一幅美丽的春日画卷。",
        image: imageAssets.news.news3,
        date: "2024-03-10",
        category: "季节资讯",
        views: 1580,
        featured: false,
      },
      {
        id: 4,
        title: "酒仙湖与知名旅行社达成战略合作",
        summary:
          "为拓展客源市场，酒仙湖景区与多家知名旅行社签署战略合作协议，共同推广湘东文化旅游。",
        image: imageAssets.news.news4,
        date: "2024-03-08",
        category: "合作动态",
        views: 720,
        featured: false,
      },
    ],
    events: [
      {
        id: 5,
        title: "2024酒仙湖春季文化节即将盛大开幕",
        summary:
          "4月1日至4月30日，酒仙湖将举办为期一个月的春季文化节，包含民俗表演、传统手工艺展示、美食品鉴等精彩活动。",
        image: imageAssets.news.event1,
        date: "2024-03-20",
        category: "文化节庆",
        views: 2100,
        featured: true,
      },
      {
        id: 6,
        title: "宝宁寺禅修体验营开始报名",
        summary:
          "为期三天两夜的禅修体验营，让您在千年古刹中感受内心的宁静与智慧，名额有限，欢迎报名参加。",
        image: imageAssets.news.event2,
        date: "2024-03-18",
        category: "禅修活动",
        views: 890,
        featured: false,
      },
      {
        id: 7,
        title: "酒仙湖摄影大赛征稿启动",
        summary:
          '以"发现酒仙湖之美"为主题的摄影大赛正式启动，丰厚奖品等您来拿，展现您眼中的酒仙湖风采。',
        image: imageAssets.news.event3,
        date: "2024-03-16",
        category: "摄影比赛",
        views: 1350,
        featured: false,
      },
    ],
    announcements: [
      {
        id: 8,
        title: "关于景区部分区域临时封闭的通知",
        summary:
          "因设施维护需要，白龙洞景点将于3月25日至3月30日临时封闭，给您带来的不便敬请谅解。",
        image: imageAssets.news.announcement1,
        date: "2024-03-22",
        category: "维护通知",
        views: 650,
        featured: false,
      },
      {
        id: 9,
        title: "酒仙湖景区门票价格调整公告",
        summary:
          "根据相关政策要求，自4月1日起，景区门票价格将进行适当调整，具体价格请查看官方公告。",
        image: imageAssets.news.announcement2,
        date: "2024-03-20",
        category: "价格公告",
        views: 1100,
        featured: false,
      },
    ],
  };

  const currentNews = newsData[activeTab as keyof typeof newsData] || [];
  const featuredNews = currentNews.find((item) => item.featured);
  const regularNews = currentNews.filter((item) => !item.featured);

  const sectionClasses = cn("py-16 bg-rice-white-100", className);

  return (
    <section className={sectionClasses}>
      <div className="container-custom">
        {/* 标题区域 */}
        <div className="text-center mb-12">
          <div className="inline-block bg-wine-red-100 text-wine-red-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            最新动态
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-ink-black-700 mb-4 brush-text">
            新闻资讯
          </h2>
          <p className="text-lg text-ink-black-500 max-w-2xl mx-auto leading-relaxed">
            了解酒仙湖最新动态，掌握第一手资讯，不错过任何精彩活动
          </p>
        </div>

        {/* 分类标签 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-full p-1 shadow-soft">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center",
                  {
                    "bg-lake-green-500 text-white shadow-md":
                      activeTab === tab.id,
                    "text-ink-black-600 hover:text-lake-green-600":
                      activeTab !== tab.id,
                  }
                )}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* 新闻内容 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* 特色新闻 */}
          {featuredNews && (
            <div className="lg:col-span-2">
              <div className="relative group cursor-pointer">
                <div className="relative overflow-hidden rounded-2xl">
                  <img
                    src={featuredNews.image}
                    alt={featuredNews.title}
                    className="w-full h-80 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                  <div className="absolute top-4 left-4">
                    <span className="bg-wine-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {featuredNews.category}
                    </span>
                  </div>
                  <div className="absolute bottom-6 left-6 right-6 text-white">
                    <h3 className="text-2xl font-bold mb-3 group-hover:text-elegant-gold-400 transition-colors">
                      {featuredNews.title}
                    </h3>
                    <p className="text-gray-200 mb-4 line-clamp-2">
                      {featuredNews.summary}
                    </p>
                    <div className="flex items-center justify-between text-sm">
                      <span>{formatDate(featuredNews.date)}</span>
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                          />
                        </svg>
                        <span>{featuredNews.views}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 新闻列表 */}
          <div className="space-y-6">
            {regularNews.slice(0, 4).map((news, index) => (
              <div
                key={news.id}
                className="animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <NewsCard
                  image={news.image}
                  title={news.title}
                  summary={news.summary}
                  date={formatDate(news.date)}
                  category={news.category}
                  onClick={() => {
                    window.location.href = `/news/${news.id}`;
                  }}
                />
              </div>
            ))}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="bg-white rounded-2xl shadow-soft p-8 mb-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-lake-green-500 mb-2">
                50+
              </div>
              <div className="text-ink-black-600">本月新闻</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-lake-green-500 mb-2">
                15
              </div>
              <div className="text-ink-black-600">精彩活动</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-lake-green-500 mb-2">
                10K+
              </div>
              <div className="text-ink-black-600">阅读量</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-lake-green-500 mb-2">
                24/7
              </div>
              <div className="text-ink-black-600">实时更新</div>
            </div>
          </div>
        </div>

        {/* 订阅通知 */}
        <div className="bg-gradient-to-r from-lake-green-500 to-elegant-gold-500 rounded-2xl p-8 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">订阅最新资讯</h3>
          <p className="text-lg mb-6 opacity-90">
            第一时间获取酒仙湖最新动态和优惠信息
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <input
              type="email"
              placeholder="请输入您的邮箱地址"
              className="flex-1 px-4 py-3 rounded-lg text-ink-black-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white"
            />
            <Button
              variant="secondary"
              size="lg"
              className="bg-white text-lake-green-600 hover:bg-gray-100 whitespace-nowrap"
            >
              立即订阅
            </Button>
          </div>
          <p className="text-sm mt-4 opacity-75">
            我们承诺保护您的隐私，不会向第三方泄露您的信息
          </p>
        </div>

        {/* 更多链接 */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg" href="/news">
            查看更多新闻
          </Button>
        </div>
      </div>
    </section>
  );
};

export default NewsSection;
