import React from 'react';
import { imageAssets } from '@/utils/imageAssets';
import FallbackImage from '@/components/ui/FallbackImage';
import { AttractionImage } from '@/components/ui/OptimizedImage';

const ImageTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">图片资源测试</h1>
        
        {/* Logo 测试 */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Logo</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg mb-2">主 Logo</h3>
              <img src={imageAssets.logo.main} alt="酒仙湖 Logo" className="w-48 h-auto" />
            </div>
            <div className="bg-gray-800 p-4 rounded-lg shadow">
              <h3 className="text-lg mb-2 text-white">白色 Logo</h3>
              <img src={imageAssets.logo.white} alt="酒仙湖白色 Logo" className="w-48 h-auto" />
            </div>
          </div>
        </section>

        {/* 轮播图测试 */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">首页轮播图</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <FallbackImage
              src={imageAssets.hero.slide1}
              alt="酒仙湖主景"
              className="w-full h-48 object-cover rounded-lg"
              fallbackType="gradient"
            />
            <FallbackImage
              src={imageAssets.hero.slide2}
              alt="宝宁寺"
              className="w-full h-48 object-cover rounded-lg"
              fallbackType="gradient"
            />
            <FallbackImage
              src={imageAssets.hero.slide3}
              alt="攸女仙境"
              className="w-full h-48 object-cover rounded-lg"
              fallbackType="gradient"
            />
            <FallbackImage
              src={imageAssets.hero.slide4}
              alt="康养度假"
              className="w-full h-48 object-cover rounded-lg"
              fallbackType="gradient"
            />
          </div>
        </section>

        {/* 景点图片测试 */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">景点图片</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <h3 className="text-sm font-medium mb-2">酒仙湖水域</h3>
              <AttractionImage
                src={imageAssets.attractions.jiuxianLake}
                alt="酒仙湖水域"
                className="rounded-lg"
              />
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">宝宁寺</h3>
              <AttractionImage
                src={imageAssets.attractions.baoningTemple}
                alt="宝宁寺"
                className="rounded-lg"
              />
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">攸女仙境</h3>
              <AttractionImage
                src={imageAssets.attractions.younvFairyland}
                alt="攸女仙境"
                className="rounded-lg"
              />
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">白龙洞</h3>
              <AttractionImage
                src={imageAssets.attractions.whiteDragonCave}
                alt="白龙洞"
                className="rounded-lg"
              />
            </div>
          </div>
        </section>

        {/* 新闻图片测试 */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">新闻图片</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FallbackImage
              src={imageAssets.news.news1}
              alt="生态旅游示范区"
              className="w-full h-40 object-cover rounded-lg"
              fallbackType="pattern"
            />
            <FallbackImage
              src={imageAssets.news.news2}
              alt="数字化升级"
              className="w-full h-40 object-cover rounded-lg"
              fallbackType="pattern"
            />
            <FallbackImage
              src={imageAssets.news.news3}
              alt="春季赏花"
              className="w-full h-40 object-cover rounded-lg"
              fallbackType="pattern"
            />
          </div>
        </section>

        {/* 回到首页 */}
        <div className="text-center">
          <a 
            href="/" 
            className="inline-block bg-lake-green-400 text-white px-6 py-3 rounded-lg hover:bg-lake-green-500 transition-colors"
          >
            返回首页
          </a>
        </div>
      </div>
    </div>
  );
};

export default ImageTest;
