import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { cn } from "@/utils";
import Button from "@/components/ui/Button";
import type { MenuItem, Language } from "@/types";

interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const location = useLocation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<Language>("zh");

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // 检查当前路径是否匹配导航项
  const isActiveRoute = (href: string): boolean => {
    if (href === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(href);
  };

  // 导航菜单数据
  const navigationItems: MenuItem[] = [
    {
      id: "home",
      label: "首页",
      labelEn: "Home",
      href: "/",
    },
    {
      id: "attractions",
      label: "景点介绍",
      labelEn: "Attractions",
      href: "/attractions",
      children: [
        {
          id: "jiuxian-lake",
          label: "酒仙湖水域",
          labelEn: "Jiuxian Lake",
          href: "/attractions/1",
        },
        {
          id: "baoning-temple",
          label: "宝宁寺",
          labelEn: "Baoning Temple",
          href: "/attractions/2",
        },
        {
          id: "younv-fairyland",
          label: "攸女仙境",
          labelEn: "Younv Fairyland",
          href: "/attractions/3",
        },
        {
          id: "white-dragon-cave",
          label: "白龙洞",
          labelEn: "White Dragon Cave",
          href: "/attractions/4",
        },
      ],
    },
    {
      id: "services",
      label: "服务设施",
      labelEn: "Services",
      href: "/services",
    },
    {
      id: "news",
      label: "新闻动态",
      labelEn: "News",
      href: "/news",
    },
    {
      id: "about",
      label: "关于我们",
      labelEn: "About",
      href: "/about",
    },
    {
      id: "contact",
      label: "联系我们",
      labelEn: "Contact",
      href: "/contact",
    },
  ];

  // 语言选项
  const languages: { code: Language; label: string; flag: string }[] = [
    { code: "zh", label: "中文", flag: "🇨🇳" },
    { code: "en", label: "English", flag: "🇺🇸" },
    { code: "fr", label: "Français", flag: "🇫🇷" },
    { code: "ru", label: "Русский", flag: "🇷🇺" },
    { code: "ar", label: "العربية", flag: "🇸🇦" },
    { code: "es", label: "Español", flag: "🇪🇸" },
  ];

  const headerClasses = cn(
    "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
    {
      "bg-white/95 backdrop-blur-md shadow-medium": isScrolled,
      "bg-transparent": !isScrolled,
    },
    className
  );

  return (
    <header className={headerClasses}>
      {/* 顶部工具栏 */}
      <div className="bg-ink-black-700 text-white py-2">
        <div className="container-custom">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>湖南省株洲市攸县酒埠江镇</span>
              </div>
              <div className="flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
                <span>400-123-4567</span>
              </div>
              <div className="flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path
                    fillRule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>今日天气：晴 25°C</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* 语言切换 */}
              <div className="relative group">
                <button className="flex items-center hover:text-elegant-gold-400 transition-colors">
                  <span className="mr-1">
                    {
                      languages.find((lang) => lang.code === currentLanguage)
                        ?.flag
                    }
                  </span>
                  <span className="mr-1">
                    {
                      languages.find((lang) => lang.code === currentLanguage)
                        ?.label
                    }
                  </span>
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <div className="absolute top-full right-0 mt-1 bg-white text-ink-black-700 rounded-lg shadow-strong py-2 min-w-32 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  {languages.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => setCurrentLanguage(lang.code)}
                      className="w-full px-4 py-2 text-left hover:bg-lake-green-50 flex items-center"
                    >
                      <span className="mr-2">{lang.flag}</span>
                      <span>{lang.label}</span>
                    </button>
                  ))}
                </div>
              </div>
              <Button variant="primary" size="sm">
                快速预订
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 主导航 */}
      <div className="py-4">
        <div className="container-custom">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center">
              <img
                src="/images/logo.png"
                alt="酒仙湖"
                className="h-12 w-auto mr-3"
              />
              <div>
                <h1 className="text-xl font-bold text-ink-black-700 brush-text">
                  酒仙湖
                </h1>
                <p className="text-xs text-ink-black-500">
                  湘东明珠·现代桃花源
                </p>
              </div>
            </div>

            {/* 桌面导航 */}
            <nav className="hidden lg:flex items-center space-x-8">
              {navigationItems.map((item) => (
                <div key={item.id} className="relative group">
                  <a
                    href={item.href}
                    className={cn(
                      "font-medium transition-colors py-2 relative",
                      isActiveRoute(item.href)
                        ? "text-lake-green-600"
                        : "text-ink-black-700 hover:text-lake-green-500"
                    )}
                  >
                    {currentLanguage === "zh" ? item.label : item.labelEn}
                    {isActiveRoute(item.href) && (
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-lake-green-600 rounded-full" />
                    )}
                  </a>
                  {item.children && (
                    <div className="absolute top-full left-0 mt-1 bg-white rounded-lg shadow-strong py-2 min-w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                      {item.children.map((child) => (
                        <a
                          key={child.id}
                          href={child.href}
                          className={cn(
                            "block px-4 py-2 transition-colors",
                            isActiveRoute(child.href)
                              ? "text-lake-green-600 bg-lake-green-50 font-medium"
                              : "text-ink-black-600 hover:text-lake-green-500 hover:bg-lake-green-50"
                          )}
                        >
                          {currentLanguage === "zh"
                            ? child.label
                            : child.labelEn}
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* 移动端菜单按钮 */}
            <button
              className="lg:hidden p-2 text-ink-black-700 hover:text-lake-green-500"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {isMobileMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* 移动端菜单 */}
      {isMobileMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200">
          <div className="container-custom py-4">
            <nav className="space-y-4">
              {navigationItems.map((item) => (
                <div key={item.id}>
                  <a
                    href={item.href}
                    className={cn(
                      "block font-medium py-2 transition-colors",
                      isActiveRoute(item.href)
                        ? "text-lake-green-600"
                        : "text-ink-black-700 hover:text-lake-green-500"
                    )}
                  >
                    {currentLanguage === "zh" ? item.label : item.labelEn}
                  </a>
                  {item.children && (
                    <div className="ml-4 space-y-2">
                      {item.children.map((child) => (
                        <a
                          key={child.id}
                          href={child.href}
                          className="block text-ink-black-600 hover:text-lake-green-500 py-1"
                        >
                          {currentLanguage === "zh"
                            ? child.label
                            : child.labelEn}
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
