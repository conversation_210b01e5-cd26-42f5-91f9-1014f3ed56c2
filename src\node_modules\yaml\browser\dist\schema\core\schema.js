import { map } from '../common/map.js';
import { nullTag } from '../common/null.js';
import { seq } from '../common/seq.js';
import { string } from '../common/string.js';
import { boolTag } from './bool.js';
import { floatNaN, floatExp, float } from './float.js';
import { intOct, int, intHex } from './int.js';

const schema = [
    map,
    seq,
    string,
    nullTag,
    boolTag,
    intOct,
    int,
    intHex,
    floatNaN,
    floatExp,
    float
];

export { schema };
