import React from 'react';
import { cn } from '@/utils';
import FallbackImage from './FallbackImage';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  backgroundImage?: string;
  className?: string;
  children?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  backgroundImage,
  className,
  children,
}) => {
  return (
    <section className={cn("relative py-20 overflow-hidden", className)}>
      {/* 背景图片 */}
      {backgroundImage && (
        <FallbackImage
          src={backgroundImage}
          alt={title}
          className="absolute inset-0 w-full h-full object-cover"
          fallbackType="gradient"
        />
      )}
      
      {/* 渐变遮罩 */}
      <div className="absolute inset-0 bg-gradient-to-r from-lake-green-600/90 via-lake-green-500/80 to-elegant-gold-400/70" />
      
      {/* 装饰性图案 */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-32 h-32 border-2 border-white/30 rounded-full" />
        <div className="absolute top-20 right-20 w-24 h-24 border border-white/20 rounded-full" />
        <div className="absolute bottom-10 left-1/4 w-16 h-16 border border-white/25 rounded-full" />
        <div className="absolute bottom-20 right-1/3 w-20 h-20 border-2 border-white/20 rounded-full" />
      </div>
      
      {/* 水墨纹理效果 */}
      <div className="absolute inset-0 bg-ink-wash opacity-30" />
      
      {/* 内容 */}
      <div className="relative container-custom text-center text-white">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 brush-text">
          {title}
        </h1>
        {subtitle && (
          <p className="text-xl text-gray-100 max-w-3xl mx-auto leading-relaxed">
            {subtitle}
          </p>
        )}
        {children}
      </div>
      
      {/* 底部装饰波浪 */}
      <div className="absolute bottom-0 left-0 w-full overflow-hidden">
        <svg
          className="relative block w-full h-12"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            fill="rgba(248, 249, 250, 0.8)"
          />
        </svg>
      </div>
    </section>
  );
};

export default PageHeader;
