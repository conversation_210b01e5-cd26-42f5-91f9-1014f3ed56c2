import React, { useState, useEffect } from "react";
import { cn } from "@/utils";
import Button, { BookingButton, LearnMoreButton } from "@/components/ui/Button";
import { imageAssets } from "@/utils/imageAssets";

interface HeroSectionProps {
  className?: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({ className }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);

  // 轮播数据
  const slides = [
    {
      id: 1,
      title: "湘东明珠·现代桃花源",
      subtitle: "酒仙湖生态旅游度假区",
      description: "国家4A级景区，总蓄水量3亿立方米，蓄水面积11.2平方公里",
      image: imageAssets.hero.slide1,
      video: "/videos/hero-video.mp4",
      cta: {
        primary: "立即预订",
        secondary: "了解更多",
      },
    },
    {
      id: 2,
      title: "千年佛教文化",
      subtitle: "宝宁寺禅宗祖庭",
      description: '享有"北有少林，南有宝宁"美誉的佛教圣地',
      image: imageAssets.hero.slide2,
      cta: {
        primary: "文化探索",
        secondary: "预约参观",
      },
    },
    {
      id: 3,
      title: "攸女仙境",
      subtitle: "神秘的攸女文化",
      description: '以"攸女文化"为主题的岛屿景点，感受湘东民俗风情',
      image: imageAssets.hero.slide3,
      cta: {
        primary: "体验之旅",
        secondary: "查看详情",
      },
    },
    {
      id: 4,
      title: "康养度假胜地",
      subtitle: "品味慢生活",
      description: "温泉洗浴、养生保健、瑜伽冥想，享受身心的完美放松",
      image: imageAssets.hero.slide4,
      cta: {
        primary: "康养预订",
        secondary: "服务详情",
      },
    },
  ];

  // 自动轮播
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 6000);

    return () => clearInterval(timer);
  }, [slides.length]);

  // 快捷服务数据
  const quickServices = [
    {
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"
          />
        </svg>
      ),
      title: "景区门票",
      description: "在线购票，扫码入园",
      href: "/tickets",
    },
    {
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
          />
        </svg>
      ),
      title: "住宿预订",
      description: "湖畔雅宿，品质之选",
      href: "/accommodation",
    },
    {
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"
          />
        </svg>
      ),
      title: "餐饮服务",
      description: "湖鲜美食，地道风味",
      href: "/dining",
    },
    {
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"
          />
        </svg>
      ),
      title: "会议会展",
      description: "专业服务，完美体验",
      href: "/mice",
    },
    {
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>
      ),
      title: "康养体验",
      description: "身心放松，健康之旅",
      href: "/wellness",
    },
    {
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
          />
        </svg>
      ),
      title: "文化探索",
      description: "千年文化，深度体验",
      href: "/culture",
    },
  ];

  const heroClasses = cn(
    "relative min-h-screen flex items-center justify-center overflow-hidden",
    className
  );

  const currentSlideData = slides[currentSlide];

  return (
    <section className={heroClasses}>
      {/* 背景视频/图片 */}
      <div className="absolute inset-0 z-0">
        {currentSlide === 0 && isVideoLoaded ? (
          <video
            className="w-full h-full object-cover"
            autoPlay
            muted
            loop
            playsInline
            onLoadedData={() => setIsVideoLoaded(true)}
          >
            <source src={currentSlideData.video} type="video/mp4" />
            <img
              src={currentSlideData.image}
              alt={currentSlideData.title}
              className="w-full h-full object-cover"
            />
          </video>
        ) : (
          <img
            src={currentSlideData.image}
            alt={currentSlideData.title}
            className="w-full h-full object-cover transition-all duration-1000"
          />
        )}

        {/* 遮罩层 */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-transparent" />

        {/* 水墨效果 */}
        <div className="absolute inset-0 ink-wash opacity-30" />
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* 文字内容 */}
          <div className="text-white animate-fade-in-left">
            <div className="mb-4">
              <span className="inline-block bg-elegant-gold-400/20 text-elegant-gold-400 px-4 py-2 rounded-full text-sm font-medium backdrop-blur-sm">
                {currentSlideData.subtitle}
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 brush-text leading-tight">
              {currentSlideData.title}
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-lg">
              {currentSlideData.description}
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <BookingButton size="lg" className="shadow-glow">
                {currentSlideData.cta.primary}
              </BookingButton>
              <LearnMoreButton
                size="lg"
                className="text-white border-white hover:bg-white hover:text-ink-black-700"
              >
                {currentSlideData.cta.secondary}
              </LearnMoreButton>
            </div>
          </div>

          {/* 快捷服务卡片 */}
          <div className="animate-fade-in-right">
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <h3 className="text-white text-xl font-semibold mb-6 text-center">
                快捷服务
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {quickServices.map((service, index) => (
                  <a
                    key={index}
                    href={service.href}
                    className="group bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center transition-all duration-300 hover:scale-105"
                  >
                    <div className="text-elegant-gold-400 mb-3 flex justify-center group-hover:scale-110 transition-transform">
                      {service.icon}
                    </div>
                    <h4 className="text-white font-medium text-sm mb-1">
                      {service.title}
                    </h4>
                    <p className="text-gray-300 text-xs">
                      {service.description}
                    </p>
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 轮播指示器 */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex space-x-3">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={cn(
                "w-3 h-3 rounded-full transition-all duration-300",
                {
                  "bg-white": index === currentSlide,
                  "bg-white/50 hover:bg-white/70": index !== currentSlide,
                }
              )}
            />
          ))}
        </div>
      </div>

      {/* 滚动提示 */}
      <div className="absolute bottom-8 right-8 z-20 animate-bounce">
        <div className="text-white text-center">
          <svg
            className="w-6 h-6 mx-auto mb-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 14l-7 7m0 0l-7-7m0 0l7-7m0 7V3"
            />
          </svg>
          <span className="text-xs">向下滚动</span>
        </div>
      </div>

      {/* 导航箭头 */}
      <button
        onClick={() =>
          setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
        }
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-300"
      >
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </svg>
      </button>
      <button
        onClick={() => setCurrentSlide((prev) => (prev + 1) % slides.length)}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-300"
      >
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>
    </section>
  );
};

export default HeroSection;
