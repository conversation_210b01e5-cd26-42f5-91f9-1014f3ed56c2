// 路由类型定义
export interface RouteConfig {
  path: string;
  name: string;
  component: string;
  meta?: {
    title?: string;
    description?: string;
    keywords?: string[];
    requiresAuth?: boolean;
  };
}

// 主要路由配置
export const routes: RouteConfig[] = [
  {
    path: '/',
    name: 'home',
    component: 'Home',
    meta: {
      title: '酒仙湖生态旅游度假区 - 湘东明珠·现代桃花源',
      description: '酒仙湖国家4A级景区，总蓄水量3亿立方米，集自然风光、佛教文化、康养度假于一体的综合性旅游目的地',
      keywords: ['酒仙湖', '攸县旅游', '湖南景区', '生态旅游', '佛教文化', '康养度假']
    }
  },
  {
    path: '/attractions',
    name: 'attractions',
    component: 'Attractions',
    meta: {
      title: '景点介绍 - 酒仙湖',
      description: '探索酒仙湖八大特色景点：湖心岛、宝宁寺、攸女仙境、白龙洞等',
      keywords: ['酒仙湖景点', '宝宁寺', '攸女仙境', '白龙洞', '湖心岛']
    }
  },
  {
    path: '/attractions/:id',
    name: 'attraction-detail',
    component: 'AttractionDetail',
    meta: {
      title: '景点详情 - 酒仙湖',
      description: '详细了解酒仙湖各个景点的特色、历史文化和游览信息'
    }
  },
  {
    path: '/services',
    name: 'services',
    component: 'Services',
    meta: {
      title: '服务设施 - 酒仙湖',
      description: '酒仙湖提供住宿、餐饮、娱乐、会议等全方位服务设施',
      keywords: ['酒仙湖住宿', '餐饮服务', '会议设施', '娱乐项目']
    }
  },
  {
    path: '/news',
    name: 'news',
    component: 'News',
    meta: {
      title: '新闻动态 - 酒仙湖',
      description: '酒仙湖最新资讯、活动公告、景区动态',
      keywords: ['酒仙湖新闻', '景区动态', '旅游资讯', '活动公告']
    }
  },
  {
    path: '/news/:id',
    name: 'news-detail',
    component: 'NewsDetail',
    meta: {
      title: '新闻详情 - 酒仙湖',
      description: '酒仙湖新闻详细内容'
    }
  },
  {
    path: '/about',
    name: 'about',
    component: 'About',
    meta: {
      title: '关于我们 - 酒仙湖',
      description: '了解酒仙湖的历史文化、发展历程和企业文化',
      keywords: ['酒仙湖历史', '企业文化', '发展历程', '景区介绍']
    }
  },
  {
    path: '/contact',
    name: 'contact',
    component: 'Contact',
    meta: {
      title: '联系我们 - 酒仙湖',
      description: '酒仙湖联系方式、地址、交通指南和在线咨询',
      keywords: ['酒仙湖联系方式', '景区地址', '交通指南', '在线咨询']
    }
  }
];

// 导航菜单配置
export interface NavItem {
  name: string;
  path: string;
  icon?: string;
  children?: NavItem[];
}

export const navigationMenu: NavItem[] = [
  {
    name: '首页',
    path: '/'
  },
  {
    name: '景点介绍',
    path: '/attractions',
    children: [
      { name: '酒仙湖水域', path: '/attractions/1' },
      { name: '宝宁寺', path: '/attractions/2' },
      { name: '攸女仙境', path: '/attractions/3' },
      { name: '白龙洞', path: '/attractions/4' },
      { name: '地质博物馆', path: '/attractions/5' },
      { name: '温泉康养中心', path: '/attractions/6' },
      { name: '官田古镇', path: '/attractions/7' },
      { name: '筏钓基地', path: '/attractions/8' }
    ]
  },
  {
    name: '服务设施',
    path: '/services'
  },
  {
    name: '新闻动态',
    path: '/news'
  },
  {
    name: '关于我们',
    path: '/about'
  },
  {
    name: '联系我们',
    path: '/contact'
  }
];
