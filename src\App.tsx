import React, { Suspense } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { PageLoading } from "@/components/ui/Loading";
import Home from "@/pages/Home";
import Test from "@/pages/Test";
import SimpleHome from "@/pages/SimpleHome";
import Attractions from "@/pages/Attractions";
import AttractionDetail from "@/pages/AttractionDetail";
import About from "@/pages/About";
import Services from "@/pages/Services";
import News from "@/pages/News";
import NewsDetail from "@/pages/NewsDetail";
import Contact from "@/pages/Contact";
import "@/styles/globals.css";

function App() {
  return (
    <Router>
      <div className="App">
        <Suspense fallback={<PageLoading />}>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/simple" element={<SimpleHome />} />
            <Route path="/test" element={<Test />} />
            <Route path="/attractions" element={<Attractions />} />
            <Route path="/attractions/:id" element={<AttractionDetail />} />
            <Route path="/about" element={<About />} />
            <Route path="/services" element={<Services />} />
            <Route path="/news" element={<News />} />
            <Route path="/news/:id" element={<NewsDetail />} />
            <Route path="/contact" element={<Contact />} />
          </Routes>
        </Suspense>
      </div>
    </Router>
  );
}

export default App;
