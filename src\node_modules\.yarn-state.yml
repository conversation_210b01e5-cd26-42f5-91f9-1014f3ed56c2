# Warning: This file is automatically generated. Removing it is fine, but will
# cause your node_modules installation to become invalidated.

__metadata:
  version: 1
  nmMode: classic

"@alloc/quick-lru@npm:5.2.0":
  locations:
    - "node_modules/@alloc/quick-lru"

"@ampproject/remapping@npm:2.3.0":
  locations:
    - "node_modules/@ampproject/remapping"

"@babel/code-frame@npm:7.27.1":
  locations:
    - "node_modules/@babel/code-frame"

"@babel/compat-data@npm:7.28.0":
  locations:
    - "node_modules/@babel/compat-data"

"@babel/core@npm:7.28.0":
  locations:
    - "node_modules/@babel/core"

"@babel/generator@npm:7.28.0":
  locations:
    - "node_modules/@babel/generator"

"@babel/helper-compilation-targets@npm:7.27.2":
  locations:
    - "node_modules/@babel/helper-compilation-targets"

"@babel/helper-globals@npm:7.28.0":
  locations:
    - "node_modules/@babel/helper-globals"

"@babel/helper-module-imports@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-module-imports"

"@babel/helper-module-transforms@virtual:2c032490421458ee4e212ed9bd0627762ff65ed1232d4208f2d615b0d0187bb07fc168cbfc1670b2da389400360e723c4eeeceee24d006e509ab345b44149a9f#npm:7.27.3":
  locations:
    - "node_modules/@babel/helper-module-transforms"

"@babel/helper-plugin-utils@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-plugin-utils"

"@babel/helper-string-parser@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-string-parser"

"@babel/helper-validator-identifier@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-identifier"

"@babel/helper-validator-option@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-option"

"@babel/helpers@npm:7.27.6":
  locations:
    - "node_modules/@babel/helpers"

"@babel/parser@npm:7.28.0":
  locations:
    - "node_modules/@babel/parser"

"@babel/plugin-transform-react-jsx-self@virtual:f60ec76f10f045cd0b60cc356593ccb6dfe90254279183c48a5ce2141f83f25f52535a38e1b8f189aa889d646160e4f5604f92e09bfe773938c875467b234805#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-self"

"@babel/plugin-transform-react-jsx-source@virtual:f60ec76f10f045cd0b60cc356593ccb6dfe90254279183c48a5ce2141f83f25f52535a38e1b8f189aa889d646160e4f5604f92e09bfe773938c875467b234805#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-source"

"@babel/template@npm:7.27.2":
  locations:
    - "node_modules/@babel/template"

"@babel/traverse@npm:7.28.0":
  locations:
    - "node_modules/@babel/traverse"

"@babel/types@npm:7.28.1":
  locations:
    - "node_modules/@babel/types"

"@esbuild/win32-x64@npm:0.18.20":
  locations:
    - "node_modules/@esbuild/win32-x64"

"@eslint-community/eslint-utils@virtual:dd20287a5a1e86b12a5b04609f98bd729fafd847d08e1fc89cdc68f92d1acf209e53b09ef0af4b6e7781d88e1f9acf94e3bf34619939e434ad5ffb0f24855eb4#npm:4.7.0":
  locations:
    - "node_modules/@eslint-community/eslint-utils"

"@eslint-community/regexpp@npm:4.12.1":
  locations:
    - "node_modules/@eslint-community/regexpp"

"@eslint/eslintrc@npm:2.1.4":
  locations:
    - "node_modules/@eslint/eslintrc"

"@eslint/js@npm:8.57.1":
  locations:
    - "node_modules/@eslint/js"

"@humanwhocodes/config-array@npm:0.13.0":
  locations:
    - "node_modules/@humanwhocodes/config-array"

"@humanwhocodes/module-importer@npm:1.0.1":
  locations:
    - "node_modules/@humanwhocodes/module-importer"

"@humanwhocodes/object-schema@npm:2.0.3":
  locations:
    - "node_modules/@humanwhocodes/object-schema"

"@isaacs/cliui@npm:8.0.2":
  locations:
    - "node_modules/@isaacs/cliui"

"@jridgewell/gen-mapping@npm:0.3.12":
  locations:
    - "node_modules/@jridgewell/gen-mapping"

"@jridgewell/resolve-uri@npm:3.1.2":
  locations:
    - "node_modules/@jridgewell/resolve-uri"

"@jridgewell/sourcemap-codec@npm:1.5.4":
  locations:
    - "node_modules/@jridgewell/sourcemap-codec"

"@jridgewell/trace-mapping@npm:0.3.29":
  locations:
    - "node_modules/@jridgewell/trace-mapping"

"@nodelib/fs.scandir@npm:2.1.5":
  locations:
    - "node_modules/@nodelib/fs.scandir"

"@nodelib/fs.stat@npm:2.0.5":
  locations:
    - "node_modules/@nodelib/fs.stat"

"@nodelib/fs.walk@npm:1.2.8":
  locations:
    - "node_modules/@nodelib/fs.walk"

"@pkgjs/parseargs@npm:0.11.0":
  locations:
    - "node_modules/@pkgjs/parseargs"

"@remix-run/router@npm:1.23.0":
  locations:
    - "node_modules/@remix-run/router"

"@rolldown/pluginutils@npm:1.0.0-beta.19":
  locations:
    - "node_modules/@rolldown/pluginutils"

"@tailwindcss/aspect-ratio@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:0.4.2":
  locations:
    - "node_modules/@tailwindcss/aspect-ratio"

"@tailwindcss/forms@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:0.5.10":
  locations:
    - "node_modules/@tailwindcss/forms"

"@tailwindcss/typography@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:0.5.16":
  locations:
    - "node_modules/@tailwindcss/typography"

"@types/babel__core@npm:7.20.5":
  locations:
    - "node_modules/@types/babel__core"

"@types/babel__generator@npm:7.27.0":
  locations:
    - "node_modules/@types/babel__generator"

"@types/babel__template@npm:7.4.4":
  locations:
    - "node_modules/@types/babel__template"

"@types/babel__traverse@npm:7.20.7":
  locations:
    - "node_modules/@types/babel__traverse"

"@types/json-schema@npm:7.0.15":
  locations:
    - "node_modules/@types/json-schema"

"@types/prop-types@npm:15.7.15":
  locations:
    - "node_modules/@types/prop-types"

"@types/react-dom@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:18.3.7":
  locations:
    - "node_modules/@types/react-dom"

"@types/react@npm:18.3.23":
  locations:
    - "node_modules/@types/react"

"@types/semver@npm:7.7.0":
  locations:
    - "node_modules/@types/semver"

"@typescript-eslint/eslint-plugin@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/eslint-plugin"

"@typescript-eslint/parser@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/parser"

"@typescript-eslint/scope-manager@npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/scope-manager"

"@typescript-eslint/type-utils@virtual:93a09225e0ac70179b489441559752f95bd6b8a9d6637ccdac11c9e1e197f6a6e322fb60941167439cef77a229e43dc036de211a82bd0f716ccef06e078e88e8#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/type-utils"

"@typescript-eslint/types@npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/types"

"@typescript-eslint/typescript-estree@virtual:b77592f1af5d61640d3db25ae7237442240dfabb7d0dac10800217ba01427a5ebd26e906c0296a29223e22705f768ceefe6de23a5dc3b56efa3b4ce2622e585d#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/typescript-estree"
  aliases:
    - "virtual:f76819aac9f6c31541cf80e1709ebca20d9df6c2895183e52bac60b02b2c1cb3f0edfe736a9a2da292ab68c9ce6babbe616f563191118b7664a6b15159aa61b3#npm:6.21.0"

"@typescript-eslint/utils@virtual:93a09225e0ac70179b489441559752f95bd6b8a9d6637ccdac11c9e1e197f6a6e322fb60941167439cef77a229e43dc036de211a82bd0f716ccef06e078e88e8#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/utils"

"@typescript-eslint/visitor-keys@npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/visitor-keys"

"@ungap/structured-clone@npm:1.3.0":
  locations:
    - "node_modules/@ungap/structured-clone"

"@vitejs/plugin-react@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:4.6.0":
  locations:
    - "node_modules/@vitejs/plugin-react"

"acorn-jsx@virtual:a50722a5a9326b6a5f12350c494c4db3aa0f4caeac45e3e9e5fe071da20014ecfe738fe2ebe2c9c98abae81a4ea86b42f56d776b3bd5ec37f9ad3670c242b242#npm:5.3.2":
  locations:
    - "node_modules/acorn-jsx"

"acorn@npm:8.15.0":
  locations:
    - "node_modules/acorn"

"ajv@npm:6.12.6":
  locations:
    - "node_modules/ajv"

"ansi-regex@npm:5.0.1":
  locations:
    - "node_modules/ansi-regex"

"ansi-regex@npm:6.1.0":
  locations:
    - "node_modules/wrap-ansi/node_modules/ansi-regex"
    - "node_modules/string-width/node_modules/ansi-regex"
    - "node_modules/@isaacs/cliui/node_modules/ansi-regex"

"ansi-styles@npm:4.3.0":
  locations:
    - "node_modules/ansi-styles"

"ansi-styles@npm:6.2.1":
  locations:
    - "node_modules/wrap-ansi/node_modules/ansi-styles"

"any-promise@npm:1.3.0":
  locations:
    - "node_modules/any-promise"

"anymatch@npm:3.1.3":
  locations:
    - "node_modules/anymatch"

"arg@npm:5.0.2":
  locations:
    - "node_modules/arg"

"argparse@npm:2.0.1":
  locations:
    - "node_modules/argparse"

"array-union@npm:2.1.0":
  locations:
    - "node_modules/array-union"

"autoprefixer@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:10.4.21":
  locations:
    - "node_modules/autoprefixer"

"balanced-match@npm:1.0.2":
  locations:
    - "node_modules/balanced-match"

"binary-extensions@npm:2.3.0":
  locations:
    - "node_modules/binary-extensions"

"brace-expansion@npm:1.1.12":
  locations:
    - "node_modules/minimatch/node_modules/brace-expansion"

"brace-expansion@npm:2.0.2":
  locations:
    - "node_modules/brace-expansion"

"braces@npm:3.0.3":
  locations:
    - "node_modules/braces"

"browserslist@npm:4.25.1":
  locations:
    - "node_modules/browserslist"

"callsites@npm:3.1.0":
  locations:
    - "node_modules/callsites"

"camelcase-css@npm:2.0.1":
  locations:
    - "node_modules/camelcase-css"

"caniuse-lite@npm:1.0.30001727":
  locations:
    - "node_modules/caniuse-lite"

"chalk@npm:4.1.2":
  locations:
    - "node_modules/chalk"

"chokidar@npm:3.6.0":
  locations:
    - "node_modules/chokidar"

"clsx@npm:2.1.1":
  locations:
    - "node_modules/clsx"

"color-convert@npm:2.0.1":
  locations:
    - "node_modules/color-convert"

"color-name@npm:1.1.4":
  locations:
    - "node_modules/color-name"

"commander@npm:4.1.1":
  locations:
    - "node_modules/commander"

"concat-map@npm:0.0.1":
  locations:
    - "node_modules/concat-map"

"convert-source-map@npm:2.0.0":
  locations:
    - "node_modules/convert-source-map"

"cross-spawn@npm:7.0.6":
  locations:
    - "node_modules/cross-spawn"

"cssesc@npm:3.0.0":
  locations:
    - "node_modules/cssesc"

"csstype@npm:3.1.3":
  locations:
    - "node_modules/csstype"

"debug@virtual:1ff4b5f90832ba0a9c93ba1223af226e44ba70c1126a3740d93562b97bc36544e896a5e95908196f7458713e6a6089a34bfc67362fc6df7fa093bd06c878be47#npm:4.4.1":
  locations:
    - "node_modules/debug"

"deep-is@npm:0.1.4":
  locations:
    - "node_modules/deep-is"

"didyoumean@npm:1.2.2":
  locations:
    - "node_modules/didyoumean"

"dir-glob@npm:3.0.1":
  locations:
    - "node_modules/dir-glob"

"dlv@npm:1.1.3":
  locations:
    - "node_modules/dlv"

"doctrine@npm:3.0.0":
  locations:
    - "node_modules/doctrine"

"eastasianwidth@npm:0.2.0":
  locations:
    - "node_modules/eastasianwidth"

"electron-to-chromium@npm:1.5.182":
  locations:
    - "node_modules/electron-to-chromium"

"emoji-regex@npm:8.0.0":
  locations:
    - "node_modules/emoji-regex"

"emoji-regex@npm:9.2.2":
  locations:
    - "node_modules/string-width/node_modules/emoji-regex"

"esbuild@npm:0.18.20":
  locations:
    - "node_modules/esbuild"

"escalade@npm:3.2.0":
  locations:
    - "node_modules/escalade"

"escape-string-regexp@npm:4.0.0":
  locations:
    - "node_modules/escape-string-regexp"

"eslint-plugin-react-hooks@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:4.6.2":
  locations:
    - "node_modules/eslint-plugin-react-hooks"

"eslint-plugin-react-refresh@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:0.4.20":
  locations:
    - "node_modules/eslint-plugin-react-refresh"

"eslint-scope@npm:7.2.2":
  locations:
    - "node_modules/eslint-scope"

"eslint-visitor-keys@npm:3.4.3":
  locations:
    - "node_modules/eslint-visitor-keys"

"eslint@npm:8.57.1":
  locations:
    - "node_modules/eslint"

"espree@npm:9.6.1":
  locations:
    - "node_modules/espree"

"esquery@npm:1.6.0":
  locations:
    - "node_modules/esquery"

"esrecurse@npm:4.3.0":
  locations:
    - "node_modules/esrecurse"

"estraverse@npm:5.3.0":
  locations:
    - "node_modules/estraverse"

"esutils@npm:2.0.3":
  locations:
    - "node_modules/esutils"

"fast-deep-equal@npm:3.1.3":
  locations:
    - "node_modules/fast-deep-equal"

"fast-glob@npm:3.3.3":
  locations:
    - "node_modules/fast-glob"

"fast-json-stable-stringify@npm:2.1.0":
  locations:
    - "node_modules/fast-json-stable-stringify"

"fast-levenshtein@npm:2.0.6":
  locations:
    - "node_modules/fast-levenshtein"

"fastq@npm:1.19.1":
  locations:
    - "node_modules/fastq"

"file-entry-cache@npm:6.0.1":
  locations:
    - "node_modules/file-entry-cache"

"fill-range@npm:7.1.1":
  locations:
    - "node_modules/fill-range"

"find-up@npm:5.0.0":
  locations:
    - "node_modules/find-up"

"flat-cache@npm:3.2.0":
  locations:
    - "node_modules/flat-cache"

"flatted@npm:3.3.3":
  locations:
    - "node_modules/flatted"

"foreground-child@npm:3.3.1":
  locations:
    - "node_modules/foreground-child"

"fraction.js@npm:4.3.7":
  locations:
    - "node_modules/fraction.js"

"fs.realpath@npm:1.0.0":
  locations:
    - "node_modules/fs.realpath"

"function-bind@npm:1.1.2":
  locations:
    - "node_modules/function-bind"

"gensync@npm:1.0.0-beta.2":
  locations:
    - "node_modules/gensync"

"glob-parent@npm:5.1.2":
  locations:
    - "node_modules/fast-glob/node_modules/glob-parent"
    - "node_modules/chokidar/node_modules/glob-parent"

"glob-parent@npm:6.0.2":
  locations:
    - "node_modules/glob-parent"

"glob@npm:10.4.5":
  locations:
    - "node_modules/sucrase/node_modules/glob"

"glob@npm:7.2.3":
  locations:
    - "node_modules/glob"

"globals@npm:13.24.0":
  locations:
    - "node_modules/globals"

"globby@npm:11.1.0":
  locations:
    - "node_modules/globby"

"graphemer@npm:1.4.0":
  locations:
    - "node_modules/graphemer"

"has-flag@npm:4.0.0":
  locations:
    - "node_modules/has-flag"

"hasown@npm:2.0.2":
  locations:
    - "node_modules/hasown"

"ignore@npm:5.3.2":
  locations:
    - "node_modules/ignore"

"import-fresh@npm:3.3.1":
  locations:
    - "node_modules/import-fresh"

"imurmurhash@npm:0.1.4":
  locations:
    - "node_modules/imurmurhash"

"inflight@npm:1.0.6":
  locations:
    - "node_modules/inflight"

"inherits@npm:2.0.4":
  locations:
    - "node_modules/inherits"

"is-binary-path@npm:2.1.0":
  locations:
    - "node_modules/is-binary-path"

"is-core-module@npm:2.16.1":
  locations:
    - "node_modules/is-core-module"

"is-extglob@npm:2.1.1":
  locations:
    - "node_modules/is-extglob"

"is-fullwidth-code-point@npm:3.0.0":
  locations:
    - "node_modules/is-fullwidth-code-point"

"is-glob@npm:4.0.3":
  locations:
    - "node_modules/is-glob"

"is-number@npm:7.0.0":
  locations:
    - "node_modules/is-number"

"is-path-inside@npm:3.0.3":
  locations:
    - "node_modules/is-path-inside"

"isexe@npm:2.0.0":
  locations:
    - "node_modules/isexe"

"jackspeak@npm:3.4.3":
  locations:
    - "node_modules/jackspeak"

"jiti@npm:1.21.7":
  locations:
    - "node_modules/jiti"

"jiuxianhu-website@workspace:.":
  locations:
    - ""
  bin:
    "node_modules/sucrase":
      "glob": "glob/dist/esm/bin.mjs"
    "node_modules/@babel/core":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/helper-compilation-targets":
      "semver": "semver/bin/semver.js"
    ".":
      "eslint": "eslint/bin/eslint.js"
      "tailwind": "tailwindcss/lib/cli.js"
      "tailwindcss": "tailwindcss/lib/cli.js"
      "tsc": "typescript/bin/tsc"
      "tsserver": "typescript/bin/tsserver"
      "autoprefixer": "autoprefixer/bin/autoprefixer"
      "vite": "vite/bin/vite.js"
      "js-yaml": "js-yaml/bin/js-yaml.js"
      "nanoid": "nanoid/bin/nanoid.cjs"
      "loose-envify": "loose-envify/cli.js"
      "jiti": "jiti/bin/jiti.js"
      "resolve": "resolve/bin/resolve"
      "sucrase": "sucrase/bin/sucrase"
      "sucrase-node": "sucrase/bin/sucrase-node"
      "mini-svg-data-uri": "mini-svg-data-uri/cli.js"
      "cssesc": "cssesc/bin/cssesc"
      "semver": "semver/bin/semver.js"
      "esbuild": "esbuild/bin/esbuild"
      "rollup": "rollup/dist/bin/rollup"
      "parser": "@babel/parser/bin/babel-parser.js"
      "browserslist": "browserslist/cli.js"
      "node-which": "which/bin/node-which"
      "acorn": "acorn/bin/acorn"
      "yaml": "yaml/bin.mjs"
      "json5": "json5/lib/cli.js"
      "update-browserslist-db": "update-browserslist-db/cli.js"
      "rimraf": "rimraf/bin.js"
      "jsesc": "jsesc/bin/jsesc"

"js-tokens@npm:4.0.0":
  locations:
    - "node_modules/js-tokens"

"js-yaml@npm:4.1.0":
  locations:
    - "node_modules/js-yaml"

"jsesc@npm:3.1.0":
  locations:
    - "node_modules/jsesc"

"json-buffer@npm:3.0.1":
  locations:
    - "node_modules/json-buffer"

"json-schema-traverse@npm:0.4.1":
  locations:
    - "node_modules/json-schema-traverse"

"json-stable-stringify-without-jsonify@npm:1.0.1":
  locations:
    - "node_modules/json-stable-stringify-without-jsonify"

"json5@npm:2.2.3":
  locations:
    - "node_modules/json5"

"keyv@npm:4.5.4":
  locations:
    - "node_modules/keyv"

"levn@npm:0.4.1":
  locations:
    - "node_modules/levn"

"lilconfig@npm:3.1.3":
  locations:
    - "node_modules/lilconfig"

"lines-and-columns@npm:1.2.4":
  locations:
    - "node_modules/lines-and-columns"

"locate-path@npm:6.0.0":
  locations:
    - "node_modules/locate-path"

"lodash.castarray@npm:4.4.0":
  locations:
    - "node_modules/lodash.castarray"

"lodash.isplainobject@npm:4.0.6":
  locations:
    - "node_modules/lodash.isplainobject"

"lodash.merge@npm:4.6.2":
  locations:
    - "node_modules/lodash.merge"

"loose-envify@npm:1.4.0":
  locations:
    - "node_modules/loose-envify"

"lru-cache@npm:10.4.3":
  locations:
    - "node_modules/lru-cache"

"lru-cache@npm:5.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache"

"merge2@npm:1.4.1":
  locations:
    - "node_modules/merge2"

"micromatch@npm:4.0.8":
  locations:
    - "node_modules/micromatch"

"mini-svg-data-uri@npm:1.4.4":
  locations:
    - "node_modules/mini-svg-data-uri"

"minimatch@npm:3.1.2":
  locations:
    - "node_modules/minimatch"

"minimatch@npm:9.0.3":
  locations:
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch"

"minimatch@npm:9.0.5":
  locations:
    - "node_modules/sucrase/node_modules/minimatch"

"minipass@npm:7.1.2":
  locations:
    - "node_modules/minipass"

"ms@npm:2.1.3":
  locations:
    - "node_modules/ms"

"mz@npm:2.7.0":
  locations:
    - "node_modules/mz"

"nanoid@npm:3.3.11":
  locations:
    - "node_modules/nanoid"

"natural-compare@npm:1.4.0":
  locations:
    - "node_modules/natural-compare"

"node-releases@npm:2.0.19":
  locations:
    - "node_modules/node-releases"

"normalize-path@npm:3.0.0":
  locations:
    - "node_modules/normalize-path"

"normalize-range@npm:0.1.2":
  locations:
    - "node_modules/normalize-range"

"object-assign@npm:4.1.1":
  locations:
    - "node_modules/object-assign"

"object-hash@npm:3.0.0":
  locations:
    - "node_modules/object-hash"

"once@npm:1.4.0":
  locations:
    - "node_modules/once"

"optionator@npm:0.9.4":
  locations:
    - "node_modules/optionator"

"p-limit@npm:3.1.0":
  locations:
    - "node_modules/p-limit"

"p-locate@npm:5.0.0":
  locations:
    - "node_modules/p-locate"

"package-json-from-dist@npm:1.0.1":
  locations:
    - "node_modules/package-json-from-dist"

"parent-module@npm:1.0.1":
  locations:
    - "node_modules/parent-module"

"path-exists@npm:4.0.0":
  locations:
    - "node_modules/path-exists"

"path-is-absolute@npm:1.0.1":
  locations:
    - "node_modules/path-is-absolute"

"path-key@npm:3.1.1":
  locations:
    - "node_modules/path-key"

"path-parse@npm:1.0.7":
  locations:
    - "node_modules/path-parse"

"path-scurry@npm:1.11.1":
  locations:
    - "node_modules/path-scurry"

"path-type@npm:4.0.0":
  locations:
    - "node_modules/path-type"

"picocolors@npm:1.1.1":
  locations:
    - "node_modules/picocolors"

"picomatch@npm:2.3.1":
  locations:
    - "node_modules/picomatch"

"pify@npm:2.3.0":
  locations:
    - "node_modules/pify"

"pirates@npm:4.0.7":
  locations:
    - "node_modules/pirates"

"postcss-import@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:15.1.0":
  locations:
    - "node_modules/postcss-import"

"postcss-js@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:4.0.1":
  locations:
    - "node_modules/postcss-js"

"postcss-load-config@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:4.0.2":
  locations:
    - "node_modules/postcss-load-config"

"postcss-nested@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:6.2.0":
  locations:
    - "node_modules/postcss-nested"

"postcss-selector-parser@npm:6.0.10":
  locations:
    - "node_modules/@tailwindcss/typography/node_modules/postcss-selector-parser"

"postcss-selector-parser@npm:6.1.2":
  locations:
    - "node_modules/postcss-selector-parser"

"postcss-value-parser@npm:4.2.0":
  locations:
    - "node_modules/postcss-value-parser"

"postcss@npm:8.5.6":
  locations:
    - "node_modules/postcss"

"prelude-ls@npm:1.2.1":
  locations:
    - "node_modules/prelude-ls"

"punycode@npm:2.3.1":
  locations:
    - "node_modules/punycode"

"queue-microtask@npm:1.2.3":
  locations:
    - "node_modules/queue-microtask"

"react-dom@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:18.3.1":
  locations:
    - "node_modules/react-dom"

"react-refresh@npm:0.17.0":
  locations:
    - "node_modules/react-refresh"

"react-router-dom@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:6.30.1":
  locations:
    - "node_modules/react-router-dom"

"react-router@virtual:dde5f28008dbfc6e411eb5acdd26cde27fcac40b85d896e2090863ab02596ca9a13af2f0ec997b2aba099800192330b74ef3d4c596d0f83c38487291f4589058#npm:6.30.1":
  locations:
    - "node_modules/react-router"

"react@npm:18.3.1":
  locations:
    - "node_modules/react"

"read-cache@npm:1.0.0":
  locations:
    - "node_modules/read-cache"

"readdirp@npm:3.6.0":
  locations:
    - "node_modules/readdirp"

"resolve-from@npm:4.0.0":
  locations:
    - "node_modules/resolve-from"

"resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d":
  locations:
    - "node_modules/resolve"

"reusify@npm:1.1.0":
  locations:
    - "node_modules/reusify"

"rimraf@npm:3.0.2":
  locations:
    - "node_modules/rimraf"

"rollup@npm:3.29.5":
  locations:
    - "node_modules/rollup"

"run-parallel@npm:1.2.0":
  locations:
    - "node_modules/run-parallel"

"scheduler@npm:0.23.2":
  locations:
    - "node_modules/scheduler"

"semver@npm:6.3.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/semver"
    - "node_modules/@babel/core/node_modules/semver"

"semver@npm:7.7.2":
  locations:
    - "node_modules/semver"

"shebang-command@npm:2.0.0":
  locations:
    - "node_modules/shebang-command"

"shebang-regex@npm:3.0.0":
  locations:
    - "node_modules/shebang-regex"

"signal-exit@npm:4.1.0":
  locations:
    - "node_modules/signal-exit"

"slash@npm:3.0.0":
  locations:
    - "node_modules/slash"

"source-map-js@npm:1.2.1":
  locations:
    - "node_modules/source-map-js"

"string-width@npm:4.2.3":
  locations:
    - "node_modules/wrap-ansi-cjs/node_modules/string-width"
    - "node_modules/string-width-cjs"

"string-width@npm:5.1.2":
  locations:
    - "node_modules/string-width"

"strip-ansi@npm:6.0.1":
  locations:
    - "node_modules/strip-ansi-cjs"
    - "node_modules/strip-ansi"

"strip-ansi@npm:7.1.0":
  locations:
    - "node_modules/wrap-ansi/node_modules/strip-ansi"
    - "node_modules/string-width/node_modules/strip-ansi"
    - "node_modules/@isaacs/cliui/node_modules/strip-ansi"

"strip-json-comments@npm:3.1.1":
  locations:
    - "node_modules/strip-json-comments"

"sucrase@npm:3.35.0":
  locations:
    - "node_modules/sucrase"

"supports-color@npm:7.2.0":
  locations:
    - "node_modules/supports-color"

"supports-preserve-symlinks-flag@npm:1.0.0":
  locations:
    - "node_modules/supports-preserve-symlinks-flag"

"tailwind-merge@npm:2.6.0":
  locations:
    - "node_modules/tailwind-merge"

"tailwindcss@npm:3.4.17":
  locations:
    - "node_modules/tailwindcss"

"text-table@npm:0.2.0":
  locations:
    - "node_modules/text-table"

"thenify-all@npm:1.6.0":
  locations:
    - "node_modules/thenify-all"

"thenify@npm:3.3.1":
  locations:
    - "node_modules/thenify"

"to-regex-range@npm:5.0.1":
  locations:
    - "node_modules/to-regex-range"

"ts-api-utils@virtual:93a09225e0ac70179b489441559752f95bd6b8a9d6637ccdac11c9e1e197f6a6e322fb60941167439cef77a229e43dc036de211a82bd0f716ccef06e078e88e8#npm:1.4.3":
  locations:
    - "node_modules/ts-api-utils"

"ts-interface-checker@npm:0.1.13":
  locations:
    - "node_modules/ts-interface-checker"

"type-check@npm:0.4.0":
  locations:
    - "node_modules/type-check"

"type-fest@npm:0.20.2":
  locations:
    - "node_modules/type-fest"

"typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=5786d5":
  locations:
    - "node_modules/typescript"

"update-browserslist-db@virtual:7df10d33cd6842659a3529d46decd4f1eeb5ec25fc4c848cff54ea69abd11a20a55277c57a073bbb3a702942d2ae57b9433c8450dcbffbc4f38ee3eb9668c39d#npm:1.1.3":
  locations:
    - "node_modules/update-browserslist-db"

"uri-js@npm:4.4.1":
  locations:
    - "node_modules/uri-js"

"util-deprecate@npm:1.0.2":
  locations:
    - "node_modules/util-deprecate"

"vite@virtual:207b520937d7f4495502c20e9e49f1b679d0e0b28179f850c79eb2203abf0ef13edc05e3571eaa1612b5a1d78cc3b9aa867d3d351bd75c5f7bee46ace988e20f#npm:4.5.14":
  locations:
    - "node_modules/vite"

"which@npm:2.0.2":
  locations:
    - "node_modules/which"

"word-wrap@npm:1.2.5":
  locations:
    - "node_modules/word-wrap"

"wrap-ansi@npm:7.0.0":
  locations:
    - "node_modules/wrap-ansi-cjs"

"wrap-ansi@npm:8.1.0":
  locations:
    - "node_modules/wrap-ansi"

"wrappy@npm:1.0.2":
  locations:
    - "node_modules/wrappy"

"yallist@npm:3.1.1":
  locations:
    - "node_modules/yallist"

"yaml@npm:2.8.0":
  locations:
    - "node_modules/yaml"

"yocto-queue@npm:0.1.0":
  locations:
    - "node_modules/yocto-queue"
