import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { cn, formatDate } from '@/utils';
import { imageAssets } from '@/utils/imageAssets';
import FallbackImage from '@/components/ui/FallbackImage';
import Button from '@/components/ui/Button';
import { NewsCard } from '@/components/ui/Card';

interface NewsData {
  id: number;
  title: string;
  summary: string;
  content: string;
  image: string;
  date: string;
  category: string;
  views: number;
  author: string;
  tags: string[];
  featured: boolean;
}

const NewsDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [news, setNews] = useState<NewsData | null>(null);
  const [relatedNews, setRelatedNews] = useState<NewsData[]>([]);
  const [loading, setLoading] = useState(true);

  // 新闻详细数据
  const newsData: Record<string, NewsData> = {
    '1': {
      id: 1,
      title: '酒仙湖景区荣获"湖南省生态旅游示范区"称号',
      summary: '近日，湖南省文化和旅游厅公布了2024年度生态旅游示范区名单，酒仙湖景区凭借其优美的自然环境和完善的生态保护措施成功入选。',
      content: `
        <p>近日，湖南省文化和旅游厅公布了2024年度生态旅游示范区名单，酒仙湖景区凭借其优美的自然环境和完善的生态保护措施成功入选，成为株洲市首个获此殊荣的景区。</p>
        
        <h3>生态保护成效显著</h3>
        <p>酒仙湖景区始终坚持"生态优先、绿色发展"的理念，在开发建设过程中严格遵循生态保护原则。景区内森林覆盖率达到85%以上，湖水水质常年保持在国家二类标准，空气质量优良率达到98%。</p>
        
        <p>为了保护湖区生态环境，景区投入大量资金建设污水处理设施，实施垃圾分类处理，建立了完善的环境监测体系。同时，景区还开展了多项生态修复工程，包括湿地恢复、植被重建等，有效改善了生态环境质量。</p>
        
        <h3>可持续发展模式</h3>
        <p>在旅游开发过程中，酒仙湖景区积极探索可持续发展模式，通过科学规划、合理布局，实现了经济效益与生态效益的双赢。景区严格控制游客承载量，实施分时段预约制度，有效保护了生态环境。</p>
        
        <p>此外，景区还大力发展生态旅游产品，推出了生态观光、环保教育、自然体验等多种旅游项目，让游客在享受美景的同时，增强环保意识。</p>
        
        <h3>未来发展规划</h3>
        <p>获得"湖南省生态旅游示范区"称号后，酒仙湖景区将继续加大生态保护力度，完善基础设施建设，提升服务质量，努力打造成为全国知名的生态旅游目的地。</p>
        
        <p>景区负责人表示，将以此次获奖为契机，进一步加强生态环境保护，创新旅游产品，为游客提供更加优质的生态旅游体验，为湖南省生态旅游发展贡献力量。</p>
      `,
      image: imageAssets.news.news1,
      date: '2024-03-15',
      category: '景区荣誉',
      views: 1250,
      author: '酒仙湖景区管委会',
      tags: ['生态旅游', '环境保护', '可持续发展', '景区荣誉'],
      featured: true
    },
    '2': {
      id: 2,
      title: '酒仙湖数字化升级项目正式启动',
      summary: '为提升游客体验，酒仙湖景区启动全面数字化升级项目，将引入VR全景游览、AR互动体验等先进技术。',
      content: `
        <p>3月12日，酒仙湖景区数字化升级项目正式启动，该项目总投资2000万元，将通过引入VR全景游览、AR互动体验、智能导览等先进技术，全面提升游客的旅游体验。</p>
        
        <h3>智慧旅游新体验</h3>
        <p>数字化升级项目包括多个方面：首先是建设VR全景游览系统，游客可以通过VR设备提前体验景区各个景点的美景；其次是开发AR互动体验项目，在宝宁寺、攸女仙境等文化景点增加AR技术，让游客更深入地了解历史文化。</p>
        
        <p>此外，景区还将推出智能导览系统，通过手机APP为游客提供个性化的游览路线推荐、语音讲解、实时导航等服务。游客只需扫描二维码，就能获得专业的导游服务。</p>
        
        <h3>数字化管理平台</h3>
        <p>项目还包括建设景区数字化管理平台，通过大数据分析、人工智能等技术，实现客流监控、安全预警、资源调配等智能化管理，提高景区运营效率。</p>
        
        <p>预计该项目将于2024年底全面完成，届时酒仙湖将成为湖南省首个全面数字化的4A级景区。</p>
      `,
      image: imageAssets.news.news2,
      date: '2024-03-12',
      category: '项目动态',
      views: 980,
      author: '技术部',
      tags: ['数字化', '智慧旅游', 'VR体验', 'AR技术'],
      featured: false
    }
  };

  // 相关新闻数据
  const allNews: NewsData[] = Object.values(newsData);

  useEffect(() => {
    if (id && newsData[id]) {
      const currentNews = newsData[id];
      setNews(currentNews);
      
      // 获取相关新闻（同类别或其他新闻）
      const related = allNews
        .filter(item => item.id !== currentNews.id)
        .slice(0, 3);
      setRelatedNews(related);
      
      // 增加浏览量（实际项目中应该调用API）
      currentNews.views += 1;
    } else {
      navigate('/news');
    }
    setLoading(false);
  }, [id, navigate]);

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lake-green-500"></div>
        </div>
      </Layout>
    );
  }

  if (!news) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-ink-black-700 mb-4">新闻不存在</h1>
            <Button onClick={() => navigate('/news')}>返回新闻列表</Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* 面包屑导航 */}
      <section className="py-4 bg-gray-50">
        <div className="container-custom">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <button onClick={() => navigate('/')} className="hover:text-lake-green-600">首页</button>
            <span>/</span>
            <button onClick={() => navigate('/news')} className="hover:text-lake-green-600">新闻动态</button>
            <span>/</span>
            <span className="text-ink-black-600">新闻详情</span>
          </nav>
        </div>
      </section>

      {/* 新闻内容 */}
      <article className="py-12">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            {/* 新闻头部 */}
            <header className="mb-8">
              <div className="flex items-center gap-4 mb-4">
                <span className="bg-wine-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {news.category}
                </span>
                {news.featured && (
                  <span className="bg-elegant-gold-400 text-ink-black-700 px-3 py-1 rounded-full text-sm font-medium">
                    推荐
                  </span>
                )}
              </div>
              
              <h1 className="text-3xl md:text-4xl font-bold text-ink-black-700 mb-4 leading-tight">
                {news.title}
              </h1>
              
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-6">
                <div className="flex items-center gap-2">
                  <span>📅</span>
                  <span>{formatDate(news.date)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>👤</span>
                  <span>{news.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>👁️</span>
                  <span>{news.views} 次浏览</span>
                </div>
              </div>
              
              <p className="text-lg text-gray-600 leading-relaxed">
                {news.summary}
              </p>
            </header>

            {/* 新闻图片 */}
            <div className="mb-8">
              <FallbackImage
                src={news.image}
                alt={news.title}
                className="w-full h-96 object-cover rounded-xl"
                fallbackType="gradient"
              />
            </div>

            {/* 新闻正文 */}
            <div 
              className="prose prose-lg max-w-none mb-8"
              dangerouslySetInnerHTML={{ __html: news.content }}
              style={{
                lineHeight: '1.8',
                color: '#4a5568'
              }}
            />

            {/* 标签 */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-ink-black-700 mb-3">相关标签</h3>
              <div className="flex flex-wrap gap-2">
                {news.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-lake-green-100 text-lake-green-700 px-3 py-1 rounded-full text-sm"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>

            {/* 分享按钮 */}
            <div className="border-t border-gray-200 pt-6 mb-12">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  分享到：
                  <button className="ml-2 text-lake-green-600 hover:text-lake-green-700">微信</button>
                  <button className="ml-2 text-lake-green-600 hover:text-lake-green-700">微博</button>
                  <button className="ml-2 text-lake-green-600 hover:text-lake-green-700">QQ</button>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigate('/news')}
                >
                  返回列表
                </Button>
              </div>
            </div>
          </div>
        </div>
      </article>

      {/* 相关新闻 */}
      {relatedNews.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="container-custom">
            <h2 className="text-2xl font-bold text-ink-black-700 mb-8 text-center">相关新闻</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedNews.map((item) => (
                <NewsCard
                  key={item.id}
                  image={item.image}
                  title={item.title}
                  summary={item.summary}
                  date={item.date}
                  category={item.category}
                  onClick={() => navigate(`/news/${item.id}`)}
                />
              ))}
            </div>
          </div>
        </section>
      )}
    </Layout>
  );
};

export default NewsDetail;
